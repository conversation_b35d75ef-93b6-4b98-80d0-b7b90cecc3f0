<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost();
            break;
        case 'PUT':
            handlePut();
            break;
        case 'DELETE':
            handleDelete();
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'طريقة غير مدعومة']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

function handleGet() {
    global $pdo;
    
    if (isset($_GET['id'])) {
        // جلب فاتورة واحدة
        $id = (int)$_GET['id'];
        getSaleDetails($id);
    } else {
        // جلب جميع المبيعات
        $search = $_GET['search'] ?? '';
        $dateFrom = $_GET['date_from'] ?? '';
        $dateTo = $_GET['date_to'] ?? '';
        $customerId = $_GET['customer_id'] ?? '';
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 50);
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT s.*, c.name as customer_name, u.full_name as cashier_name
                FROM sales s 
                LEFT JOIN customers c ON s.customer_id = c.id
                LEFT JOIN users u ON s.user_id = u.id
                WHERE 1=1";
        $params = [];
        
        if (!empty($search)) {
            $sql .= " AND (s.invoice_number LIKE ? OR c.name LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($dateFrom)) {
            $sql .= " AND DATE(s.created_at) >= ?";
            $params[] = $dateFrom;
        }
        
        if (!empty($dateTo)) {
            $sql .= " AND DATE(s.created_at) <= ?";
            $params[] = $dateTo;
        }
        
        if (!empty($customerId)) {
            $sql .= " AND s.customer_id = ?";
            $params[] = $customerId;
        }
        
        $sql .= " ORDER BY s.created_at DESC LIMIT $limit OFFSET $offset";
        
        $sales = fetchAll($sql, $params);
        
        // حساب العدد الكلي
        $countSql = "SELECT COUNT(*) as total FROM sales s 
                     LEFT JOIN customers c ON s.customer_id = c.id 
                     WHERE 1=1";
        $countParams = [];
        
        if (!empty($search)) {
            $countSql .= " AND (s.invoice_number LIKE ? OR c.name LIKE ?)";
            $countParams[] = "%$search%";
            $countParams[] = "%$search%";
        }
        
        if (!empty($dateFrom)) {
            $countSql .= " AND DATE(s.created_at) >= ?";
            $countParams[] = $dateFrom;
        }
        
        if (!empty($dateTo)) {
            $countSql .= " AND DATE(s.created_at) <= ?";
            $countParams[] = $dateTo;
        }
        
        if (!empty($customerId)) {
            $countSql .= " AND s.customer_id = ?";
            $countParams[] = $customerId;
        }
        
        $total = fetchOne($countSql, $countParams)['total'];
        
        echo json_encode([
            'sales' => $sales,
            'total' => $total,
            'page' => $page,
            'pages' => ceil($total / $limit)
        ]);
    }
}

function handlePost() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (empty($input['items']) || !is_array($input['items'])) {
        http_response_code(400);
        echo json_encode(['error' => 'عناصر الفاتورة مطلوبة']);
        return;
    }
    
    if (empty($input['total']) || $input['total'] <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'مجموع الفاتورة غير صحيح']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // إنشاء رقم فاتورة
        $invoiceNumber = generateInvoiceNumber();
        
        // إدراج الفاتورة الرئيسية
        $sql = "INSERT INTO sales (invoice_number, customer_id, user_id, payment_type, subtotal, discount, total, paid_amount, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $invoiceNumber,
            $input['customer_id'] ?? null,
            $_SESSION['user_id'],
            $input['payment_type'] ?? 'cash',
            (float)$input['subtotal'],
            (float)($input['discount'] ?? 0),
            (float)$input['total'],
            (float)($input['paid_amount'] ?? $input['total']),
            sanitize($input['notes'] ?? '')
        ];
        
        if (!executeQuery($sql, $params)) {
            throw new Exception('خطأ في حفظ الفاتورة');
        }
        
        $saleId = getLastInsertId();
        
        // إدراج عناصر الفاتورة
        foreach ($input['items'] as $item) {
            // التحقق من توفر المنتج
            $product = fetchOne("SELECT * FROM products WHERE id = ? AND is_active = 1", [$item['id']]);
            if (!$product) {
                throw new Exception("المنتج غير موجود: " . $item['name']);
            }
            
            if ($product['quantity'] < $item['quantity']) {
                throw new Exception("الكمية المطلوبة غير متوفرة للمنتج: " . $item['name']);
            }
            
            // إدراج عنصر الفاتورة
            $itemSql = "INSERT INTO sale_items (sale_id, product_id, quantity, price, total) VALUES (?, ?, ?, ?, ?)";
            $itemParams = [
                $saleId,
                $item['id'],
                $item['quantity'],
                $item['price'],
                $item['total']
            ];
            
            if (!executeQuery($itemSql, $itemParams)) {
                throw new Exception('خطأ في حفظ عناصر الفاتورة');
            }
            
            // تحديث كمية المنتج
            $updateSql = "UPDATE products SET quantity = quantity - ? WHERE id = ?";
            if (!executeQuery($updateSql, [$item['quantity'], $item['id']])) {
                throw new Exception('خطأ في تحديث كمية المنتج');
            }
        }
        
        // تحديث رصيد العميل إذا كان البيع آجل
        if ($input['payment_type'] === 'credit' && !empty($input['customer_id'])) {
            $remaining = $input['total'] - ($input['paid_amount'] ?? 0);
            if ($remaining > 0) {
                $updateCustomerSql = "UPDATE customers SET total_debt = total_debt + ? WHERE id = ?";
                executeQuery($updateCustomerSql, [$remaining, $input['customer_id']]);
            }
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ الفاتورة بنجاح',
            'invoice_id' => $saleId,
            'invoice_number' => $invoiceNumber
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function handlePut() {
    global $pdo;
    
    // التحقق من الصلاحيات
    if ($_SESSION['user_role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'غير مصرح لك بهذه العملية']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف الفاتورة مطلوب']);
        return;
    }
    
    $id = (int)$input['id'];
    
    // التحقق من وجود الفاتورة
    $sale = fetchOne("SELECT * FROM sales WHERE id = ?", [$id]);
    if (!$sale) {
        http_response_code(404);
        echo json_encode(['error' => 'الفاتورة غير موجودة']);
        return;
    }
    
    // تحديث ملاحظات الفاتورة فقط
    $sql = "UPDATE sales SET notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $params = [
        sanitize($input['notes'] ?? $sale['notes']),
        $id
    ];
    
    if (executeQuery($sql, $params)) {
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الفاتورة بنجاح'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في تحديث الفاتورة']);
    }
}

function handleDelete() {
    global $pdo;
    
    // التحقق من الصلاحيات
    if ($_SESSION['user_role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'غير مصرح لك بهذه العملية']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف الفاتورة مطلوب']);
        return;
    }
    
    $id = (int)$input['id'];
    
    try {
        $pdo->beginTransaction();
        
        // جلب تفاصيل الفاتورة
        $sale = fetchOne("SELECT * FROM sales WHERE id = ?", [$id]);
        if (!$sale) {
            throw new Exception('الفاتورة غير موجودة');
        }
        
        // جلب عناصر الفاتورة
        $items = fetchAll("SELECT * FROM sale_items WHERE sale_id = ?", [$id]);
        
        // إرجاع الكميات للمخزون
        foreach ($items as $item) {
            $updateSql = "UPDATE products SET quantity = quantity + ? WHERE id = ?";
            executeQuery($updateSql, [$item['quantity'], $item['product_id']]);
        }
        
        // تحديث رصيد العميل
        if ($sale['customer_id']) {
            $remaining = $sale['total'] - $sale['paid_amount'];
            if ($remaining > 0) {
                $updateCustomerSql = "UPDATE customers SET total_debt = total_debt - ? WHERE id = ?";
                executeQuery($updateCustomerSql, [$remaining, $sale['customer_id']]);
            }
        }
        
        // حذف عناصر الفاتورة
        executeQuery("DELETE FROM sale_items WHERE sale_id = ?", [$id]);
        
        // حذف الفاتورة
        executeQuery("DELETE FROM sales WHERE id = ?", [$id]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الفاتورة بنجاح'
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getSaleDetails($saleId) {
    global $pdo;
    
    // جلب تفاصيل الفاتورة
    $sale = fetchOne("
        SELECT s.*, c.name as customer_name, c.phone as customer_phone, u.full_name as cashier_name
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.id = ?
    ", [$saleId]);
    
    if (!$sale) {
        http_response_code(404);
        echo json_encode(['error' => 'الفاتورة غير موجودة']);
        return;
    }
    
    // جلب عناصر الفاتورة
    $items = fetchAll("
        SELECT si.*, p.name as product_name, p.barcode
        FROM sale_items si
        LEFT JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = ?
    ", [$saleId]);
    
    echo json_encode([
        'sale' => $sale,
        'items' => $items
    ]);
}

function generateInvoiceNumber() {
    $date = date('Ymd');
    $random = rand(1000, 9999);
    return $date . $random;
}
?>
