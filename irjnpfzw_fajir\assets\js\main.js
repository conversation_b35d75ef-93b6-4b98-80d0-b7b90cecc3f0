// الوظائف الرئيسية للنظام
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات الحركة
    addAnimations();
    
    // تهيئة الأحداث
    initializeEvents();
    
    // تحديث الوقت
    updateTime();
    setInterval(updateTime, 1000);
});

// إضافة تأثيرات الحركة
function addAnimations() {
    const elements = document.querySelectorAll('.nav-item, .stat-card');
    elements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.1}s`;
        element.classList.add('fade-in');
    });
}

// تهيئة الأحداث
function initializeEvents() {
    // تأثيرات hover للبطاقات
    const cards = document.querySelectorAll('.nav-item, .stat-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // تأثيرات الأزرار
    const buttons = document.querySelectorAll('button, .btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            createRipple(e, this);
        });
    });
}

// تأثير الموجة عند النقر
function createRipple(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');
    
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// تحديث الوقت
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const dateString = now.toLocaleDateString('ar-EG', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    const timeElement = document.getElementById('current-time');
    const dateElement = document.getElementById('current-date');
    
    if (timeElement) timeElement.textContent = timeString;
    if (dateElement) dateElement.textContent = dateString;
}

// دالة لعرض الرسائل
function showMessage(message, type = 'info', duration = 3000) {
    const messageContainer = document.createElement('div');
    messageContainer.className = `message message-${type}`;
    messageContainer.innerHTML = `
        <i class="fas fa-${getMessageIcon(type)}"></i>
        <span>${message}</span>
        <button class="message-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(messageContainer);
    
    // إضافة تأثير الظهور
    setTimeout(() => {
        messageContainer.classList.add('show');
    }, 100);
    
    // إزالة الرسالة تلقائياً
    setTimeout(() => {
        messageContainer.classList.remove('show');
        setTimeout(() => {
            if (messageContainer.parentElement) {
                messageContainer.remove();
            }
        }, 300);
    }, duration);
}

// الحصول على أيقونة الرسالة
function getMessageIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// دالة لتأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return new Promise((resolve) => {
        const modal = document.createElement('div');
        modal.className = 'confirm-modal';
        modal.innerHTML = `
            <div class="confirm-content">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>تأكيد الحذف</h3>
                <p>${message}</p>
                <div class="confirm-buttons">
                    <button class="btn btn-danger" onclick="resolveConfirm(true)">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button class="btn btn-secondary" onclick="resolveConfirm(false)">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        window.resolveConfirm = function(result) {
            modal.remove();
            delete window.resolveConfirm;
            resolve(result);
        };
        
        // إضافة تأثير الظهور
        setTimeout(() => {
            modal.classList.add('show');
        }, 100);
    });
}

// دالة لتنسيق الأرقام
function formatNumber(number) {
    return new Intl.NumberFormat('ar-EG').format(number);
}

// دالة لتنسيق المبلغ
function formatMoney(amount) {
    return formatNumber(amount) + ' د.ع';
}

// دالة لتنسيق التاريخ
function formatDate(date) {
    return new Date(date).toLocaleDateString('ar-EG');
}

// دالة لتنسيق التاريخ والوقت
function formatDateTime(datetime) {
    return new Date(datetime).toLocaleString('ar-EG');
}

// دالة للبحث في الجداول
function searchTable(searchInput, tableId) {
    const filter = searchInput.value.toLowerCase();
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tr');
    
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');
        let found = false;
        
        for (let j = 0; j < cells.length; j++) {
            const cell = cells[j];
            if (cell.textContent.toLowerCase().includes(filter)) {
                found = true;
                break;
            }
        }
        
        row.style.display = found ? '' : 'none';
    }
}

// دالة لطباعة الصفحة
function printPage() {
    window.print();
}

// دالة لتصدير البيانات إلى Excel
function exportToExcel(tableId, filename = 'data') {
    const table = document.getElementById(tableId);
    const wb = XLSX.utils.table_to_book(table);
    XLSX.writeFile(wb, `${filename}.xlsx`);
}

// دالة لتحميل البيانات عبر AJAX
function loadData(url, callback) {
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (callback) callback(data);
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('حدث خطأ في تحميل البيانات', 'error');
        });
}

// دالة لإرسال البيانات عبر AJAX
function sendData(url, data, callback) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (callback) callback(result);
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('حدث خطأ في إرسال البيانات', 'error');
    });
}

// دالة للتحقق من صحة البريد الإلكتروني
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// دالة للتحقق من صحة رقم الهاتف
function validatePhone(phone) {
    const re = /^[0-9+\-\s()]+$/;
    return re.test(phone);
}

// دالة لإنشاء باركود
function generateBarcode() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `${timestamp}${random}`.slice(-12);
}

// دالة لقراءة الباركود (تحتاج مكتبة خارجية)
function scanBarcode(callback) {
    // هذه الدالة تحتاج إلى مكتبة قراءة الباركود
    // يمكن استخدام QuaggaJS أو ZXing
    if (callback) {
        callback('123456789012'); // مثال
    }
}

// دالة لحفظ البيانات في التخزين المحلي
function saveToLocalStorage(key, data) {
    localStorage.setItem(key, JSON.stringify(data));
}

// دالة لجلب البيانات من التخزين المحلي
function getFromLocalStorage(key) {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : null;
}

// دالة لمسح البيانات من التخزين المحلي
function removeFromLocalStorage(key) {
    localStorage.removeItem(key);
}

// إضافة CSS للرسائل والنوافذ المنبثقة
const style = document.createElement('style');
style.textContent = `
    .message {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        gap: 10px;
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
    }
    
    .message.show {
        transform: translateX(0);
    }
    
    .message-success { border-left: 4px solid #10b981; }
    .message-error { border-left: 4px solid #ef4444; }
    .message-warning { border-left: 4px solid #f59e0b; }
    .message-info { border-left: 4px solid #2563eb; }
    
    .message-close {
        background: none;
        border: none;
        cursor: pointer;
        padding: 5px;
        margin-right: auto;
    }
    
    .confirm-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1001;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .confirm-modal.show {
        opacity: 1;
    }
    
    .confirm-content {
        background: white;
        padding: 30px;
        border-radius: 12px;
        text-align: center;
        max-width: 400px;
        transform: scale(0.8);
        transition: transform 0.3s ease;
    }
    
    .confirm-modal.show .confirm-content {
        transform: scale(1);
    }
    
    .confirm-buttons {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255,255,255,0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
