<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

try {
    // جلب حالة الصيانة الحالية
    $maintenance = fetchAll("
        SELECT id, customer_name, device_type, status, received_date
        FROM maintenance 
        WHERE status IN ('received', 'in_progress', 'ready')
        ORDER BY received_date DESC
        LIMIT 10
    ");
    
    echo json_encode($maintenance);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في جلب حالة الصيانة: ' . $e->getMessage()]);
}
?>
