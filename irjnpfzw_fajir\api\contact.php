<?php
header('Content-Type: application/json; charset=utf-8');
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مدعومة']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (empty($input['name']) || empty($input['phone']) || empty($input['message'])) {
        http_response_code(400);
        echo json_encode(['error' => 'الاسم ورقم الهاتف والرسالة مطلوبة']);
        exit();
    }
    
    // إنشاء جدول الرسائل إذا لم يكن موجوداً
    $createTableSql = "
        CREATE TABLE IF NOT EXISTS contact_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            subject VARCHAR(200),
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    
    executeQuery($createTableSql);
    
    // حفظ الرسالة
    $sql = "INSERT INTO contact_messages (name, phone, subject, message) VALUES (?, ?, ?, ?)";
    $params = [
        sanitize($input['name']),
        sanitize($input['phone']),
        sanitize($input['subject'] ?? 'استفسار عام'),
        sanitize($input['message'])
    ];
    
    if (executeQuery($sql, $params)) {
        echo json_encode([
            'success' => true,
            'message' => 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً'
        ]);
    } else {
        throw new Exception('خطأ في حفظ الرسالة');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في إرسال الرسالة: ' . $e->getMessage()]);
}
?>
