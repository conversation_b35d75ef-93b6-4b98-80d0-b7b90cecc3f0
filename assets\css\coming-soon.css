/* صفحات قيد التطوير */

.coming-soon {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 40px 20px;
}

.coming-soon-content {
    text-align: center;
    max-width: 600px;
    background: var(--white);
    padding: 60px 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.coming-soon-content i {
    font-size: 5rem;
    color: var(--primary-color);
    margin-bottom: 30px;
    opacity: 0.8;
}

.coming-soon-content h2 {
    font-size: 2.5rem;
    color: var(--dark-color);
    margin-bottom: 20px;
    font-weight: 700;
}

.coming-soon-content p {
    font-size: 1.2rem;
    color: var(--gray-600);
    margin-bottom: 40px;
    line-height: 1.6;
}

.features-list {
    margin: 40px 0;
}

.features-list ul {
    list-style: none;
    padding: 0;
    text-align: right;
    max-width: 400px;
    margin: 0 auto;
}

.features-list li {
    padding: 12px 0;
    font-size: 1.1rem;
    color: var(--gray-700);
    display: flex;
    align-items: center;
    gap: 15px;
}

.features-list li i {
    color: var(--success-color);
    font-size: 1rem;
    width: 20px;
}

.coming-soon-content .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: var(--transition);
}

.coming-soon-content .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
    .coming-soon-content {
        padding: 40px 20px;
    }
    
    .coming-soon-content i {
        font-size: 4rem;
    }
    
    .coming-soon-content h2 {
        font-size: 2rem;
    }
    
    .coming-soon-content p {
        font-size: 1rem;
    }
    
    .features-list li {
        font-size: 1rem;
    }
}
