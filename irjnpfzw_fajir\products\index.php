<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام إدارة المحل</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/products.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                    <h1><i class="fas fa-boxes"></i> إدارة المنتجات</h1>
                </div>
                <div class="header-right">
                    <a href="../index.php" class="back-btn">
                        <i class="fas fa-arrow-right"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </header>

        <!-- أدوات التحكم -->
        <div class="controls-section">
            <div class="search-controls">
                <div class="search-box">
                    <input type="text" id="search-input" placeholder="البحث بالاسم أو الباركود...">
                    <button id="search-btn" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="filter-controls">
                    <select id="category-filter">
                        <option value="">جميع الفئات</option>
                        <option value="mobile">موبايل</option>
                        <option value="accessories">اكسسوارات</option>
                        <option value="parts">قطع غيار</option>
                    </select>
                    
                    <select id="stock-filter">
                        <option value="">جميع المنتجات</option>
                        <option value="low">مخزون منخفض</option>
                        <option value="out">نفد المخزون</option>
                    </select>
                </div>
            </div>

            <div class="action-controls">
                <?php if ($user_role === 'admin'): ?>
                <button id="add-product" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة منتج
                </button>
                <button id="import-excel" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> استيراد Excel
                </button>
                <?php endif; ?>
                <button id="export-excel" class="btn btn-secondary">
                    <i class="fas fa-download"></i> تصدير Excel
                </button>
            </div>
        </div>

        <!-- جدول المنتجات -->
        <div class="products-table-container">
            <table class="products-table" id="products-table">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>اسم المنتج</th>
                        <th>الباركود</th>
                        <th>الفئة</th>
                        <th>سعر الشراء</th>
                        <th>سعر البيع</th>
                        <th>الكمية</th>
                        <th>الحد الأدنى</th>
                        <th>الحالة</th>
                        <?php if ($user_role === 'admin'): ?>
                        <th>الإجراءات</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody id="products-tbody">
                    <!-- سيتم تحميل البيانات هنا -->
                </tbody>
            </table>
        </div>

        <!-- التصفح -->
        <div class="pagination" id="pagination">
            <!-- أزرار التصفح -->
        </div>
    </div>

    <!-- نافذة إضافة/تعديل منتج -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">إضافة منتج جديد</h3>
                <button class="close-modal">&times;</button>
            </div>
            <form id="product-form">
                <input type="hidden" id="product-id" name="id">
                
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم المنتج *</label>
                        <input type="text" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>الباركود</label>
                        <div class="barcode-input">
                            <input type="text" name="barcode">
                            <button type="button" id="generate-barcode" class="btn-small">
                                <i class="fas fa-magic"></i> توليد
                            </button>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>الفئة *</label>
                        <select name="category" required>
                            <option value="">اختر الفئة</option>
                            <option value="mobile">موبايل</option>
                            <option value="accessories">اكسسوارات</option>
                            <option value="parts">قطع غيار</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>الصورة</label>
                        <input type="file" name="image" accept="image/*">
                    </div>
                </div>

                <div class="form-group">
                    <label>الوصف</label>
                    <textarea name="description" rows="3"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>سعر الشراء</label>
                        <input type="number" name="purchase_price" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>سعر البيع *</label>
                        <input type="number" name="selling_price" min="0" step="0.01" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>الكمية</label>
                        <input type="number" name="quantity" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>الحد الأدنى</label>
                        <input type="number" name="min_quantity" min="1" value="5">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="btn btn-secondary close-modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة استيراد Excel -->
    <div id="import-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>استيراد منتجات من Excel</h3>
                <button class="close-modal">&times;</button>
            </div>
            <form id="import-form" enctype="multipart/form-data">
                <div class="form-group">
                    <label>ملف Excel</label>
                    <input type="file" name="excel_file" accept=".xlsx,.xls" required>
                    <small>يجب أن يحتوي الملف على الأعمدة: الاسم، الباركود، الفئة، سعر البيع، الكمية</small>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> استيراد
                    </button>
                    <button type="button" class="btn btn-secondary close-modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/products.js"></script>
    <script>
        // تهيئة صفحة المنتجات
        document.addEventListener('DOMContentLoaded', function() {
            ProductsManager.init();
        });
    </script>
</body>
</html>
