<?php
header('Content-Type: application/json; charset=utf-8');
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'GET') {
        if (isset($_GET['id'])) {
            // جلب منتج واحد
            $productId = (int)$_GET['id'];
            $product = fetchOne("
                SELECT id, name, category, selling_price, quantity, image, barcode, description
                FROM products 
                WHERE id = ? AND is_active = 1 AND quantity > 0
            ", [$productId]);
            
            if ($product) {
                echo json_encode($product);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'المنتج غير موجود أو غير متوفر']);
            }
        } else {
            // جلب جميع المنتجات المتاحة للعملاء
            $category = $_GET['category'] ?? '';
            $search = $_GET['search'] ?? '';
            
            $sql = "
                SELECT id, name, category, selling_price, quantity, image, barcode, description
                FROM products 
                WHERE is_active = 1 AND quantity > 0
            ";
            $params = [];
            
            if (!empty($category) && $category !== 'all') {
                $sql .= " AND category = ?";
                $params[] = $category;
            }
            
            if (!empty($search)) {
                $sql .= " AND (name LIKE ? OR description LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }
            
            $sql .= " ORDER BY name ASC LIMIT 100";
            
            $products = fetchAll($sql, $params) ?: [];
            
            echo json_encode($products);
        }
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'طريقة غير مدعومة']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}
?>
