<?php
header('Content-Type: application/json; charset=utf-8');
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مدعومة']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (empty($input['customer_name']) || empty($input['customer_phone']) || 
        empty($input['device_type']) || empty($input['problem_description'])) {
        http_response_code(400);
        echo json_encode(['error' => 'جميع البيانات مطلوبة']);
        exit();
    }
    
    // إنشاء جدول طلبات الصيانة إذا لم يكن موجوداً
    $createTableSql = "
        CREATE TABLE IF NOT EXISTS maintenance_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            customer_name VARCHAR(100) NOT NULL,
            customer_phone VARCHAR(20) NOT NULL,
            device_type VARCHAR(100) NOT NULL,
            device_model VARCHAR(100),
            problem_description TEXT NOT NULL,
            status ENUM('pending', 'contacted', 'scheduled', 'received') DEFAULT 'pending',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    
    executeQuery($createTableSql);
    
    // حفظ طلب الصيانة
    $sql = "INSERT INTO maintenance_requests (customer_name, customer_phone, device_type, device_model, problem_description) 
            VALUES (?, ?, ?, ?, ?)";
    $params = [
        sanitize($input['customer_name']),
        sanitize($input['customer_phone']),
        sanitize($input['device_type']),
        sanitize($input['device_model'] ?? ''),
        sanitize($input['problem_description'])
    ];
    
    if (executeQuery($sql, $params)) {
        $requestId = getLastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إرسال طلب الصيانة بنجاح. سنتواصل معك لتحديد موعد الاستلام',
            'request_id' => $requestId
        ]);
    } else {
        throw new Exception('خطأ في حفظ طلب الصيانة');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في إرسال طلب الصيانة: ' . $e->getMessage()]);
}
?>
