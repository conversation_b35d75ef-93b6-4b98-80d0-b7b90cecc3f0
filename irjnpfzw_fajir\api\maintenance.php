<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost();
            break;
        case 'PUT':
            handlePut();
            break;
        case 'DELETE':
            handleDelete();
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'طريقة غير مدعومة']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

function handleGet() {
    global $pdo;
    
    if (isset($_GET['id'])) {
        // جلب جهاز واحد
        $id = (int)$_GET['id'];
        $maintenance = fetchOne("
            SELECT m.*, c.name as customer_name_linked, u.full_name as user_name
            FROM maintenance m 
            LEFT JOIN customers c ON m.customer_id = c.id
            LEFT JOIN users u ON m.user_id = u.id
            WHERE m.id = ?
        ", [$id]);
        
        if ($maintenance) {
            echo json_encode($maintenance);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'الجهاز غير موجود']);
        }
    } else {
        // جلب جميع الأجهزة
        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? '';
        $date = $_GET['date'] ?? '';
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT m.*, u.full_name as user_name
                FROM maintenance m 
                LEFT JOIN users u ON m.user_id = u.id
                WHERE 1=1";
        $params = [];
        
        if (!empty($search)) {
            $sql .= " AND (m.customer_name LIKE ? OR m.customer_phone LIKE ? OR m.device_type LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($status)) {
            $sql .= " AND m.status = ?";
            $params[] = $status;
        }
        
        if (!empty($date)) {
            $sql .= " AND DATE(m.received_date) = ?";
            $params[] = $date;
        }
        
        $sql .= " ORDER BY m.received_date DESC LIMIT $limit OFFSET $offset";
        
        $maintenance = fetchAll($sql, $params);
        
        // حساب العدد الكلي
        $countSql = "SELECT COUNT(*) as total FROM maintenance m WHERE 1=1";
        $countParams = [];
        
        if (!empty($search)) {
            $countSql .= " AND (m.customer_name LIKE ? OR m.customer_phone LIKE ? OR m.device_type LIKE ?)";
            $countParams[] = "%$search%";
            $countParams[] = "%$search%";
            $countParams[] = "%$search%";
        }
        
        if (!empty($status)) {
            $countSql .= " AND m.status = ?";
            $countParams[] = $status;
        }
        
        if (!empty($date)) {
            $countSql .= " AND DATE(m.received_date) = ?";
            $countParams[] = $date;
        }
        
        $total = fetchOne($countSql, $countParams)['total'];
        
        echo json_encode([
            'maintenance' => $maintenance,
            'total' => $total,
            'page' => $page,
            'pages' => ceil($total / $limit)
        ]);
    }
}

function handlePost() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (empty($input['customer_name']) || empty($input['customer_phone']) || 
        empty($input['device_type']) || empty($input['problem_description'])) {
        http_response_code(400);
        echo json_encode(['error' => 'البيانات المطلوبة ناقصة']);
        return;
    }
    
    // البحث عن العميل أو إنشاء عميل جديد
    $customerId = null;
    if (!empty($input['customer_phone'])) {
        $customer = fetchOne("SELECT id FROM customers WHERE phone = ? AND is_active = 1", [$input['customer_phone']]);
        if ($customer) {
            $customerId = $customer['id'];
        } else {
            // إنشاء عميل جديد
            $customerSql = "INSERT INTO customers (name, phone, is_active) VALUES (?, ?, 1)";
            if (executeQuery($customerSql, [$input['customer_name'], $input['customer_phone']])) {
                $customerId = getLastInsertId();
            }
        }
    }
    
    $sql = "INSERT INTO maintenance (customer_id, customer_name, customer_phone, device_type, device_model, 
            problem_description, estimated_cost, actual_cost, status, notes, technician_notes, user_id) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'received', ?, ?, ?)";
    
    $params = [
        $customerId,
        sanitize($input['customer_name']),
        sanitize($input['customer_phone']),
        sanitize($input['device_type']),
        sanitize($input['device_model'] ?? ''),
        sanitize($input['problem_description']),
        (float)($input['estimated_cost'] ?? 0),
        (float)($input['actual_cost'] ?? 0),
        sanitize($input['notes'] ?? ''),
        sanitize($input['technician_notes'] ?? ''),
        $_SESSION['user_id']
    ];
    
    if (executeQuery($sql, $params)) {
        $maintenanceId = getLastInsertId();
        echo json_encode([
            'success' => true,
            'message' => 'تم استقبال الجهاز بنجاح',
            'maintenance_id' => $maintenanceId
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في حفظ بيانات الجهاز']);
    }
}

function handlePut() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف الجهاز مطلوب']);
        return;
    }
    
    $id = (int)$input['id'];
    
    // التحقق من وجود الجهاز
    $maintenance = fetchOne("SELECT * FROM maintenance WHERE id = ?", [$id]);
    if (!$maintenance) {
        http_response_code(404);
        echo json_encode(['error' => 'الجهاز غير موجود']);
        return;
    }
    
    // تحديث الحالة إذا تم تغييرها إلى "مسلم"
    $deliveryDate = null;
    if (isset($input['status']) && $input['status'] === 'delivered' && $maintenance['status'] !== 'delivered') {
        $deliveryDate = date('Y-m-d H:i:s');
    }
    
    // بناء استعلام التحديث
    $updateFields = [];
    $params = [];
    
    if (isset($input['customer_name'])) {
        $updateFields[] = "customer_name = ?";
        $params[] = sanitize($input['customer_name']);
    }
    
    if (isset($input['customer_phone'])) {
        $updateFields[] = "customer_phone = ?";
        $params[] = sanitize($input['customer_phone']);
    }
    
    if (isset($input['device_type'])) {
        $updateFields[] = "device_type = ?";
        $params[] = sanitize($input['device_type']);
    }
    
    if (isset($input['device_model'])) {
        $updateFields[] = "device_model = ?";
        $params[] = sanitize($input['device_model']);
    }
    
    if (isset($input['problem_description'])) {
        $updateFields[] = "problem_description = ?";
        $params[] = sanitize($input['problem_description']);
    }
    
    if (isset($input['estimated_cost'])) {
        $updateFields[] = "estimated_cost = ?";
        $params[] = (float)$input['estimated_cost'];
    }
    
    if (isset($input['actual_cost'])) {
        $updateFields[] = "actual_cost = ?";
        $params[] = (float)$input['actual_cost'];
    }
    
    if (isset($input['status'])) {
        $updateFields[] = "status = ?";
        $params[] = $input['status'];
    }
    
    if (isset($input['notes'])) {
        $updateFields[] = "notes = ?";
        $params[] = sanitize($input['notes']);
    }
    
    if (isset($input['technician_notes'])) {
        $updateFields[] = "technician_notes = ?";
        $params[] = sanitize($input['technician_notes']);
    }
    
    if ($deliveryDate) {
        $updateFields[] = "delivery_date = ?";
        $params[] = $deliveryDate;
    }
    
    $updateFields[] = "updated_at = CURRENT_TIMESTAMP";
    $params[] = $id;
    
    $sql = "UPDATE maintenance SET " . implode(', ', $updateFields) . " WHERE id = ?";
    
    if (executeQuery($sql, $params)) {
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث بيانات الجهاز بنجاح'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في تحديث بيانات الجهاز']);
    }
}

function handleDelete() {
    global $pdo;
    
    // التحقق من الصلاحيات
    if ($_SESSION['user_role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'غير مصرح لك بهذه العملية']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف الجهاز مطلوب']);
        return;
    }
    
    $id = (int)$input['id'];
    
    // التحقق من وجود الجهاز
    $maintenance = fetchOne("SELECT * FROM maintenance WHERE id = ?", [$id]);
    if (!$maintenance) {
        http_response_code(404);
        echo json_encode(['error' => 'الجهاز غير موجود']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // حذف قطع الغيار المرتبطة
        executeQuery("DELETE FROM maintenance_parts WHERE maintenance_id = ?", [$id]);
        
        // حذف الجهاز
        executeQuery("DELETE FROM maintenance WHERE id = ?", [$id]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الجهاز بنجاح'
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}
?>
