/* صفحة إدارة العملاء */

/* محتوى الصفحة */
.page-content {
    padding: 30px;
}

/* أدوات التحكم */
.controls-section {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.search-controls {
    display: flex;
    gap: 15px;
    flex: 1;
}

.search-box {
    display: flex;
    gap: 10px;
    min-width: 350px;
}

.search-box input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px 18px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: #1d4ed8;
}

.filter-controls select {
    padding: 12px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    background: var(--white);
    min-width: 180px;
    font-size: 1rem;
}

.action-controls {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 0.95rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-secondary {
    background: var(--gray-500);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-small {
    padding: 8px 15px;
    font-size: 0.85rem;
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card:nth-child(1) {
    border-left-color: var(--primary-color);
}

.stat-card:nth-child(2) {
    border-left-color: var(--success-color);
}

.stat-card:nth-child(3) {
    border-left-color: var(--warning-color);
}

.stat-card:nth-child(4) {
    border-left-color: var(--danger-color);
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--white);
}

.stat-card:nth-child(1) .stat-icon {
    background: var(--primary-color);
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--success-color);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--warning-color);
}

.stat-card:nth-child(4) .stat-icon {
    background: var(--danger-color);
}

.stat-info h3 {
    font-size: 1rem;
    color: var(--gray-600);
    margin-bottom: 8px;
    font-weight: 600;
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: var(--dark-color);
    margin: 0;
}

/* حاوي الجدول */
.table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 25px;
}

.table-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gray-50);
}

.table-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-header h3 i {
    color: var(--primary-color);
}

.table-actions {
    display: flex;
    gap: 10px;
}

.table-wrapper {
    overflow-x: auto;
}

/* الجدول */
.customers-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.customers-table th {
    background: var(--gray-50);
    padding: 15px 12px;
    text-align: center;
    font-weight: 700;
    border-bottom: 2px solid var(--gray-200);
    color: var(--gray-700);
    white-space: nowrap;
}

.customers-table td {
    padding: 15px 12px;
    text-align: center;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
}

.customers-table tbody tr:hover {
    background: var(--gray-50);
}

.customer-name {
    font-weight: 600;
    color: var(--dark-color);
    text-align: right;
}

.customer-phone {
    font-family: 'Courier New', monospace;
    background: var(--gray-100);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
    direction: ltr;
}

.customer-address {
    max-width: 200px;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.debt-amount {
    font-weight: 700;
    font-size: 1.1rem;
}

.debt-amount.positive {
    color: var(--danger-color);
}

.debt-amount.zero {
    color: var(--success-color);
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
}

.actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 5px;
    text-decoration: none;
}

.view-btn {
    background: var(--primary-color);
    color: var(--white);
}

.edit-btn {
    background: var(--warning-color);
    color: var(--white);
}

.payment-btn {
    background: var(--success-color);
    color: var(--white);
}

.delete-btn {
    background: var(--danger-color);
    color: var(--white);
}

.action-btn:hover {
    transform: scale(1.05);
}

/* التصفح */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 25px 0;
}

.pagination button {
    padding: 10px 15px;
    border: 1px solid var(--gray-300);
    background: var(--white);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.pagination button:hover {
    background: var(--gray-100);
}

.pagination button.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease-out;
}

.modal-content.large {
    max-width: 900px;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gray-50);
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
    font-weight: 700;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-500);
    padding: 5px;
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 25px;
}

/* النماذج */
#customer-form,
#payment-form {
    padding: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--gray-700);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group input[readonly] {
    background: var(--gray-100);
    cursor: not-allowed;
}

.form-actions {
    padding: 20px 25px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    background: var(--gray-50);
}

/* كشف الحساب */
.statement-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.summary-card {
    background: var(--gray-50);
    padding: 20px;
    border-radius: var(--border-radius);
    text-align: center;
    border-left: 4px solid var(--primary-color);
}

.summary-card h4 {
    font-size: 0.9rem;
    color: var(--gray-600);
    margin-bottom: 8px;
}

.summary-card .amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
}

.statement-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.statement-table th,
.statement-table td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid var(--gray-200);
}

.statement-table th {
    background: var(--gray-50);
    font-weight: 600;
}

.transaction-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.type-sale {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.type-payment {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .controls-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-controls {
        flex-direction: column;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .action-controls {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .page-content {
        padding: 20px;
    }
    
    .table-wrapper {
        overflow-x: auto;
    }
    
    .customers-table {
        min-width: 800px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .actions {
        flex-direction: column;
    }
    
    .action-btn {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
}

/* تأثيرات التحميل */
.loading {
    text-align: center;
    padding: 40px;
    color: var(--gray-500);
}

.loading i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* رسائل فارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: var(--gray-300);
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--gray-600);
}

/* تحسينات إضافية */
.highlight {
    background: rgba(255, 235, 59, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
}

.tooltip {
    position: relative;
    cursor: help;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dark-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1001;
}
