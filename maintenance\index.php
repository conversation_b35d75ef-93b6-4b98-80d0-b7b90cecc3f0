<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الصيانة - نظام إدارة المحل</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/maintenance.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <span>إدارة المحل</span>
                </div>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="../admin/dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li>
                        <a href="../pos/index.php" class="nav-link">
                            <i class="fas fa-cash-register"></i>
                            <span>نقطة البيع</span>
                        </a>
                    </li>
                    <li>
                        <a href="../products/index.php" class="nav-link">
                            <i class="fas fa-boxes"></i>
                            <span>إدارة المنتجات</span>
                        </a>
                    </li>
                    <li>
                        <a href="../customers/index.php" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>إدارة العملاء</span>
                        </a>
                    </li>
                    <li>
                        <a href="../suppliers/index.php" class="nav-link">
                            <i class="fas fa-truck"></i>
                            <span>إدارة الموردين</span>
                        </a>
                    </li>
                    <li>
                        <a href="index.php" class="nav-link active">
                            <i class="fas fa-tools"></i>
                            <span>نظام الصيانة</span>
                        </a>
                    </li>
                    <li>
                        <a href="../reports/index.php" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <?php if ($user_role === 'admin'): ?>
                    <li>
                        <a href="../settings/index.php" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                        <span class="user-role"><?php echo $user_role === 'admin' ? 'مدير' : 'موظف'; ?></span>
                    </div>
                </div>
                <a href="../auth/logout.php" class="logout-btn" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>نظام الصيانة</h1>
                    <div class="breadcrumb">
                        <span>الرئيسية</span>
                        <i class="fas fa-chevron-left"></i>
                        <span>نظام الصيانة</span>
                    </div>
                </div>
                
                <div class="top-bar-right">
                    <div class="datetime">
                        <div id="current-time"></div>
                        <div id="current-date"></div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                <!-- Controls Section -->
                <div class="controls-section">
                    <div class="search-controls">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="البحث برقم الهاتف أو نوع الجهاز...">
                            <button id="search-btn" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        
                        <div class="filter-controls">
                            <select id="status-filter">
                                <option value="">جميع الحالات</option>
                                <option value="received">مستلم</option>
                                <option value="in_progress">قيد التصليح</option>
                                <option value="ready">جاهز للتسليم</option>
                                <option value="delivered">مسلم</option>
                            </select>
                            
                            <input type="date" id="date-filter" class="date-filter">
                        </div>
                    </div>

                    <div class="action-controls">
                        <button id="receive-device" class="btn btn-primary">
                            <i class="fas fa-plus"></i> استقبال جهاز
                        </button>
                        <button id="print-receipt" class="btn btn-secondary" disabled>
                            <i class="fas fa-print"></i> طباعة إيصال
                        </button>
                        <button id="export-report" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> تقرير الصيانة
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card received">
                        <div class="stat-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <div class="stat-info">
                            <h3>أجهزة مستلمة</h3>
                            <p class="stat-value" id="received-count">0</p>
                        </div>
                    </div>
                    
                    <div class="stat-card progress">
                        <div class="stat-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="stat-info">
                            <h3>قيد التصليح</h3>
                            <p class="stat-value" id="progress-count">0</p>
                        </div>
                    </div>
                    
                    <div class="stat-card ready">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>جاهزة للتسليم</h3>
                            <p class="stat-value" id="ready-count">0</p>
                        </div>
                    </div>
                    
                    <div class="stat-card delivered">
                        <div class="stat-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="stat-info">
                            <h3>مسلمة</h3>
                            <p class="stat-value" id="delivered-count">0</p>
                        </div>
                    </div>
                </div>

                <!-- Maintenance Table -->
                <div class="table-container">
                    <div class="table-header">
                        <h3><i class="fas fa-tools"></i> قائمة الصيانة</h3>
                        <div class="table-actions">
                            <button class="btn-small" onclick="MaintenanceManager.loadMaintenance()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="maintenance-table" id="maintenance-table">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>اسم العميل</th>
                                    <th>رقم الهاتف</th>
                                    <th>نوع الجهاز</th>
                                    <th>المشكلة</th>
                                    <th>التكلفة المقدرة</th>
                                    <th>التكلفة الفعلية</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الاستلام</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="maintenance-tbody">
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination" id="pagination">
                    <!-- أزرار التصفح -->
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة استقبال جهاز -->
    <div id="device-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">استقبال جهاز للصيانة</h3>
                <button class="close-modal">&times;</button>
            </div>
            <form id="device-form">
                <input type="hidden" id="maintenance-id" name="id">
                
                <div class="form-section">
                    <h4><i class="fas fa-user"></i> بيانات العميل</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم العميل *</label>
                            <input type="text" name="customer_name" required>
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف *</label>
                            <input type="tel" name="customer_phone" required>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h4><i class="fas fa-mobile-alt"></i> بيانات الجهاز</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>نوع الجهاز *</label>
                            <input type="text" name="device_type" placeholder="مثال: iPhone 13" required>
                        </div>
                        <div class="form-group">
                            <label>موديل الجهاز</label>
                            <input type="text" name="device_model" placeholder="مثال: Pro Max">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>وصف المشكلة *</label>
                        <textarea name="problem_description" rows="4" placeholder="اشرح المشكلة بالتفصيل..." required></textarea>
                    </div>
                </div>

                <div class="form-section">
                    <h4><i class="fas fa-dollar-sign"></i> التكلفة والملاحظات</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>التكلفة المقدرة</label>
                            <input type="number" name="estimated_cost" min="0" step="0.01" value="0">
                        </div>
                        <div class="form-group">
                            <label>التكلفة الفعلية</label>
                            <input type="number" name="actual_cost" min="0" step="0.01" value="0">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea name="notes" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>ملاحظات الفني</label>
                        <textarea name="technician_notes" rows="3"></textarea>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="btn btn-secondary close-modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تحديث الحالة -->
    <div id="status-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تحديث حالة الصيانة</h3>
                <button class="close-modal">&times;</button>
            </div>
            <form id="status-form">
                <input type="hidden" id="status-maintenance-id" name="id">
                
                <div class="form-group">
                    <label>الحالة الجديدة *</label>
                    <select name="status" required>
                        <option value="received">مستلم</option>
                        <option value="in_progress">قيد التصليح</option>
                        <option value="ready">جاهز للتسليم</option>
                        <option value="delivered">مسلم</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>التكلفة الفعلية</label>
                    <input type="number" name="actual_cost" min="0" step="0.01">
                </div>

                <div class="form-group">
                    <label>ملاحظات الفني</label>
                    <textarea name="technician_notes" rows="4"></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> تحديث الحالة
                    </button>
                    <button type="button" class="btn btn-secondary close-modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة قطع الغيار -->
    <div id="parts-modal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>قطع الغيار المستخدمة</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="parts-content">
                    <!-- سيتم تحميل قطع الغيار هنا -->
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script src="../assets/js/maintenance.js"></script>
    <script>
        // تهيئة صفحة الصيانة
        document.addEventListener('DOMContentLoaded', function() {
            MaintenanceManager.init();
        });
    </script>
</body>
</html>
