/* الخطوط والألوان الأساسية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --white: #ffffff;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    --border-radius: 8px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: var(--white);
    padding: 20px 0;
    margin-bottom: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.header h1 {
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 15px;
}

.header h1 i {
    font-size: 2.5rem;
    color: #fbbf24;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 1.1rem;
}

.role {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.logout-btn {
    background: var(--danger-color);
    color: var(--white);
    padding: 10px 20px;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background: #dc2626;
    transform: translateY(-2px);
}

/* Navigation Menu */
.main-nav {
    margin-bottom: 30px;
}

.nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.nav-item {
    background: var(--white);
    padding: 30px 20px;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    text-align: center;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #1d4ed8);
    transform: scaleX(0);
    transition: var(--transition);
}

.nav-item:hover::before {
    transform: scaleX(1);
}

.nav-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.nav-item i {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.nav-item span {
    font-size: 1.2rem;
    font-weight: 600;
}

/* ألوان مختلفة لكل قسم */
.nav-item.pos i { color: var(--success-color); }
.nav-item.products i { color: var(--primary-color); }
.nav-item.customers i { color: var(--warning-color); }
.nav-item.suppliers i { color: #8b5cf6; }
.nav-item.maintenance i { color: var(--danger-color); }
.nav-item.reports i { color: #06b6d4; }
.nav-item.settings i { color: var(--secondary-color); }

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.stat-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    padding: 15px;
    border-radius: 50%;
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-info h3 {
    font-size: 1rem;
    color: var(--gray-600);
    margin-bottom: 5px;
}

.stat-info p {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
}

/* ألوان مختلفة للإحصائيات */
.stat-card:nth-child(1) { border-left-color: var(--success-color); }
.stat-card:nth-child(1) i { color: var(--success-color); background: rgba(16, 185, 129, 0.1); }

.stat-card:nth-child(2) { border-left-color: var(--primary-color); }
.stat-card:nth-child(2) i { color: var(--primary-color); background: rgba(37, 99, 235, 0.1); }

.stat-card:nth-child(3) { border-left-color: var(--warning-color); }
.stat-card:nth-child(3) i { color: var(--warning-color); background: rgba(245, 158, 11, 0.1); }

.stat-card:nth-child(4) { border-left-color: var(--danger-color); }
.stat-card:nth-child(4) i { color: var(--danger-color); background: rgba(239, 68, 68, 0.1); }

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .nav-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .nav-item {
        padding: 20px 15px;
    }
    
    .nav-item i {
        font-size: 2.5rem;
    }
    
    .nav-item span {
        font-size: 1rem;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .stat-card i {
        font-size: 2rem;
        width: 60px;
        height: 60px;
    }
    
    .stat-info p {
        font-size: 1.5rem;
    }
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
