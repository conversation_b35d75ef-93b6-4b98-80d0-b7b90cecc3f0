// إدارة المنتجات
const ProductsManager = {
    currentPage: 1,
    totalPages: 1,
    products: [],
    filters: {
        search: '',
        category: '',
        stock: ''
    },

    // تهيئة النظام
    init() {
        this.bindEvents();
        this.loadProducts();
    },

    // ربط الأحداث
    bindEvents() {
        // البحث
        document.getElementById('search-input').addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.currentPage = 1;
            this.loadProducts();
        });

        // الفلاتر
        document.getElementById('category-filter').addEventListener('change', (e) => {
            this.filters.category = e.target.value;
            this.currentPage = 1;
            this.loadProducts();
        });

        document.getElementById('stock-filter').addEventListener('change', (e) => {
            this.filters.stock = e.target.value;
            this.currentPage = 1;
            this.loadProducts();
        });

        // أزرار العمليات
        document.getElementById('add-product')?.addEventListener('click', () => {
            this.showProductModal();
        });

        document.getElementById('import-excel')?.addEventListener('click', () => {
            this.showImportModal();
        });

        document.getElementById('export-excel').addEventListener('click', () => {
            this.exportToExcel();
        });

        // النماذج
        document.getElementById('product-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveProduct();
        });

        document.getElementById('import-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.importFromExcel();
        });

        // توليد باركود
        document.getElementById('generate-barcode').addEventListener('click', () => {
            this.generateBarcode();
        });

        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                this.hideModals();
            });
        });

        // إغلاق النوافذ عند النقر خارجها
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModals();
                }
            });
        });
    },

    // تحميل المنتجات
    async loadProducts() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: 20,
                search: this.filters.search,
                category: this.filters.category
            });

            // تطبيق فلتر المخزون
            if (this.filters.stock === 'low') {
                params.append('low_stock', '1');
            } else if (this.filters.stock === 'out') {
                params.append('out_of_stock', '1');
            }

            const response = await fetch(`../api/products.php?${params}`);
            const data = await response.json();

            if (response.ok) {
                this.products = data.products;
                this.totalPages = data.pages;
                this.displayProducts();
                this.updatePagination();
            } else {
                throw new Error(data.error || 'خطأ في تحميل المنتجات');
            }
        } catch (error) {
            console.error('Error loading products:', error);
            showMessage('خطأ في تحميل المنتجات: ' + error.message, 'error');
            this.showEmptyState();
        }
    },

    // عرض المنتجات
    displayProducts() {
        const tbody = document.getElementById('products-tbody');
        
        if (this.products.length === 0) {
            this.showEmptyState();
            return;
        }

        tbody.innerHTML = this.products.map(product => `
            <tr>
                <td>
                    <img src="${product.image || '../assets/images/no-image.png'}" 
                         alt="${product.name}" class="product-image">
                </td>
                <td class="product-name">${product.name}</td>
                <td><span class="barcode">${product.barcode || '-'}</span></td>
                <td>
                    <span class="category-badge category-${product.category}">
                        ${this.getCategoryName(product.category)}
                    </span>
                </td>
                <td class="price">${formatMoney(product.purchase_price || 0)}</td>
                <td class="price">${formatMoney(product.selling_price)}</td>
                <td class="quantity ${this.getQuantityClass(product)}">${product.quantity}</td>
                <td>${product.min_quantity}</td>
                <td>
                    <span class="status-badge status-${product.is_active ? 'active' : 'inactive'}">
                        ${product.is_active ? 'نشط' : 'معطل'}
                    </span>
                </td>
                ${this.canEdit() ? `
                <td class="actions">
                    <button class="action-btn edit-btn" onclick="ProductsManager.editProduct(${product.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="ProductsManager.deleteProduct(${product.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
                ` : ''}
            </tr>
        `).join('');
    },

    // عرض حالة فارغة
    showEmptyState() {
        const tbody = document.getElementById('products-tbody');
        const colspan = this.canEdit() ? 10 : 9;
        
        tbody.innerHTML = `
            <tr>
                <td colspan="${colspan}" class="empty-state">
                    <i class="fas fa-box-open"></i>
                    <h3>لا توجد منتجات</h3>
                    <p>لم يتم العثور على منتجات تطابق البحث</p>
                </td>
            </tr>
        `;
    },

    // عرض حالة التحميل
    showLoading() {
        const tbody = document.getElementById('products-tbody');
        const colspan = this.canEdit() ? 10 : 9;
        
        tbody.innerHTML = `
            <tr>
                <td colspan="${colspan}" class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>جاري التحميل...</p>
                </td>
            </tr>
        `;
    },

    // تحديث التصفح
    updatePagination() {
        const pagination = document.getElementById('pagination');
        
        if (this.totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let html = '';
        
        // زر السابق
        html += `
            <button ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="ProductsManager.goToPage(${this.currentPage - 1})">
                <i class="fas fa-chevron-right"></i> السابق
            </button>
        `;

        // أرقام الصفحات
        for (let i = 1; i <= this.totalPages; i++) {
            if (i === this.currentPage || 
                i === 1 || 
                i === this.totalPages || 
                (i >= this.currentPage - 1 && i <= this.currentPage + 1)) {
                html += `
                    <button class="${i === this.currentPage ? 'active' : ''}" 
                            onclick="ProductsManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.currentPage - 2 || i === this.currentPage + 2) {
                html += '<span>...</span>';
            }
        }

        // زر التالي
        html += `
            <button ${this.currentPage === this.totalPages ? 'disabled' : ''} 
                    onclick="ProductsManager.goToPage(${this.currentPage + 1})">
                التالي <i class="fas fa-chevron-left"></i>
            </button>
        `;

        pagination.innerHTML = html;
    },

    // الانتقال لصفحة
    goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            this.loadProducts();
        }
    },

    // عرض نافذة المنتج
    showProductModal(product = null) {
        const modal = document.getElementById('product-modal');
        const form = document.getElementById('product-form');
        const title = document.getElementById('modal-title');
        
        if (product) {
            title.textContent = 'تعديل المنتج';
            this.fillProductForm(product);
        } else {
            title.textContent = 'إضافة منتج جديد';
            form.reset();
            document.getElementById('product-id').value = '';
        }
        
        modal.classList.add('show');
    },

    // ملء نموذج المنتج
    fillProductForm(product) {
        document.getElementById('product-id').value = product.id;
        document.querySelector('[name="name"]').value = product.name;
        document.querySelector('[name="barcode"]').value = product.barcode || '';
        document.querySelector('[name="category"]').value = product.category;
        document.querySelector('[name="description"]').value = product.description || '';
        document.querySelector('[name="purchase_price"]').value = product.purchase_price || '';
        document.querySelector('[name="selling_price"]').value = product.selling_price;
        document.querySelector('[name="quantity"]').value = product.quantity;
        document.querySelector('[name="min_quantity"]').value = product.min_quantity;
    },

    // حفظ المنتج
    async saveProduct() {
        try {
            const form = document.getElementById('product-form');
            const formData = new FormData(form);
            const productId = document.getElementById('product-id').value;
            
            // تحويل FormData إلى Object
            const data = {};
            for (let [key, value] of formData.entries()) {
                if (key !== 'image') {
                    data[key] = value;
                }
            }
            
            const url = '../api/products.php';
            const method = productId ? 'PUT' : 'POST';
            
            if (productId) {
                data.id = productId;
            }
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage(result.message, 'success');
                this.hideModals();
                this.loadProducts();
            } else {
                throw new Error(result.error || 'خطأ في حفظ المنتج');
            }
        } catch (error) {
            console.error('Error saving product:', error);
            showMessage('خطأ في حفظ المنتج: ' + error.message, 'error');
        }
    },

    // تعديل منتج
    editProduct(id) {
        const product = this.products.find(p => p.id === id);
        if (product) {
            this.showProductModal(product);
        }
    },

    // حذف منتج
    async deleteProduct(id) {
        const confirmed = await confirmDelete('هل أنت متأكد من حذف هذا المنتج؟');
        if (!confirmed) return;
        
        try {
            const response = await fetch('../api/products.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id: id })
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage(result.message, 'success');
                this.loadProducts();
            } else {
                throw new Error(result.error || 'خطأ في حذف المنتج');
            }
        } catch (error) {
            console.error('Error deleting product:', error);
            showMessage('خطأ في حذف المنتج: ' + error.message, 'error');
        }
    },

    // توليد باركود
    generateBarcode() {
        const barcodeInput = document.querySelector('[name="barcode"]');
        const barcode = Date.now().toString().slice(-12);
        barcodeInput.value = barcode;
    },

    // عرض نافذة الاستيراد
    showImportModal() {
        document.getElementById('import-modal').classList.add('show');
    },

    // استيراد من Excel
    async importFromExcel() {
        try {
            const form = document.getElementById('import-form');
            const formData = new FormData(form);
            
            const response = await fetch('../api/import_products.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage(`تم استيراد ${result.imported} منتج بنجاح`, 'success');
                this.hideModals();
                this.loadProducts();
            } else {
                throw new Error(result.error || 'خطأ في استيراد الملف');
            }
        } catch (error) {
            console.error('Error importing products:', error);
            showMessage('خطأ في استيراد الملف: ' + error.message, 'error');
        }
    },

    // تصدير إلى Excel
    async exportToExcel() {
        try {
            const response = await fetch('../api/export_products.php');
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `products_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showMessage('تم تصدير المنتجات بنجاح', 'success');
            } else {
                throw new Error('خطأ في تصدير الملف');
            }
        } catch (error) {
            console.error('Error exporting products:', error);
            showMessage('خطأ في تصدير الملف: ' + error.message, 'error');
        }
    },

    // إخفاء النوافذ المنبثقة
    hideModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
        });
    },

    // الحصول على اسم الفئة
    getCategoryName(category) {
        const categories = {
            'mobile': 'موبايل',
            'accessories': 'اكسسوارات',
            'parts': 'قطع غيار'
        };
        return categories[category] || category;
    },

    // الحصول على فئة الكمية
    getQuantityClass(product) {
        if (product.quantity === 0) return 'out';
        if (product.quantity <= product.min_quantity) return 'low';
        return '';
    },

    // التحقق من صلاحية التعديل
    canEdit() {
        // يمكن إضافة منطق الصلاحيات هنا
        return true; // مؤقتاً
    }
};
