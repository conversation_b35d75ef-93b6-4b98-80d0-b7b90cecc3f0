// إدارة العملاء
const CustomersManager = {
    currentPage: 1,
    totalPages: 1,
    customers: [],
    filters: {
        search: '',
        debt: ''
    },

    // تهيئة النظام
    init() {
        this.bindEvents();
        this.loadCustomers();
        this.loadStats();
        
        // تهيئة الشريط الجانبي
        AdminDashboard.init();
    },

    // ربط الأحداث
    bindEvents() {
        // البحث
        document.getElementById('search-input').addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.currentPage = 1;
            this.loadCustomers();
        });

        // فلتر الديون
        document.getElementById('debt-filter').addEventListener('change', (e) => {
            this.filters.debt = e.target.value;
            this.currentPage = 1;
            this.loadCustomers();
        });

        // أزرار العمليات
        document.getElementById('add-customer').addEventListener('click', () => {
            this.showCustomerModal();
        });

        document.getElementById('export-excel').addEventListener('click', () => {
            this.exportToExcel();
        });

        document.getElementById('import-excel').addEventListener('click', () => {
            this.importFromExcel();
        });

        // النماذج
        document.getElementById('customer-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCustomer();
        });

        document.getElementById('payment-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePayment();
        });

        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                this.hideModals();
            });
        });

        // إغلاق النوافذ عند النقر خارجها
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModals();
                }
            });
        });
    },

    // تحميل العملاء
    async loadCustomers() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: 20,
                search: this.filters.search,
                debt_filter: this.filters.debt
            });

            const response = await fetch(`../api/customers.php?${params}`);
            const data = await response.json();

            if (response.ok) {
                this.customers = data.customers;
                this.totalPages = data.pages;
                this.displayCustomers();
                this.updatePagination();
            } else {
                throw new Error(data.error || 'خطأ في تحميل العملاء');
            }
        } catch (error) {
            console.error('Error loading customers:', error);
            showMessage('خطأ في تحميل العملاء: ' + error.message, 'error');
            this.showEmptyState();
        }
    },

    // تحميل الإحصائيات
    async loadStats() {
        try {
            const response = await fetch('../api/customers_stats.php');
            const stats = await response.json();

            if (response.ok) {
                document.getElementById('total-customers').textContent = stats.total_customers;
                document.getElementById('new-customers').textContent = stats.new_customers;
                document.getElementById('customers-with-debt').textContent = stats.customers_with_debt;
                document.getElementById('total-debt').textContent = formatMoney(stats.total_debt);
            }
        } catch (error) {
            console.error('Error loading stats:', error);
        }
    },

    // عرض العملاء
    displayCustomers() {
        const tbody = document.getElementById('customers-tbody');
        
        if (this.customers.length === 0) {
            this.showEmptyState();
            return;
        }

        tbody.innerHTML = this.customers.map(customer => `
            <tr>
                <td class="customer-name">${customer.name}</td>
                <td>
                    ${customer.phone ? `<span class="customer-phone">${customer.phone}</span>` : '-'}
                </td>
                <td class="customer-address" title="${customer.address || ''}">${customer.address || '-'}</td>
                <td>
                    <span class="debt-amount ${parseFloat(customer.total_debt) > 0 ? 'positive' : 'zero'}">
                        ${formatMoney(customer.total_debt || 0)}
                    </span>
                </td>
                <td>${formatDate(customer.created_at)}</td>
                <td>
                    <span class="status-badge status-${customer.is_active ? 'active' : 'inactive'}">
                        ${customer.is_active ? 'نشط' : 'معطل'}
                    </span>
                </td>
                <td class="actions">
                    <button class="action-btn view-btn" onclick="CustomersManager.viewStatement(${customer.id})" title="كشف الحساب">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit-btn" onclick="CustomersManager.editCustomer(${customer.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${parseFloat(customer.total_debt) > 0 ? `
                    <button class="action-btn payment-btn" onclick="CustomersManager.showPaymentModal(${customer.id})" title="إضافة دفعة">
                        <i class="fas fa-money-bill"></i>
                    </button>
                    ` : ''}
                    <button class="action-btn delete-btn" onclick="CustomersManager.deleteCustomer(${customer.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    },

    // عرض حالة فارغة
    showEmptyState() {
        const tbody = document.getElementById('customers-tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>لا يوجد عملاء</h3>
                    <p>لم يتم العثور على عملاء تطابق البحث</p>
                </td>
            </tr>
        `;
    },

    // عرض حالة التحميل
    showLoading() {
        const tbody = document.getElementById('customers-tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>جاري التحميل...</p>
                </td>
            </tr>
        `;
    },

    // تحديث التصفح
    updatePagination() {
        const pagination = document.getElementById('pagination');
        
        if (this.totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let html = '';
        
        // زر السابق
        html += `
            <button ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="CustomersManager.goToPage(${this.currentPage - 1})">
                <i class="fas fa-chevron-right"></i> السابق
            </button>
        `;

        // أرقام الصفحات
        for (let i = 1; i <= this.totalPages; i++) {
            if (i === this.currentPage || 
                i === 1 || 
                i === this.totalPages || 
                (i >= this.currentPage - 1 && i <= this.currentPage + 1)) {
                html += `
                    <button class="${i === this.currentPage ? 'active' : ''}" 
                            onclick="CustomersManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.currentPage - 2 || i === this.currentPage + 2) {
                html += '<span>...</span>';
            }
        }

        // زر التالي
        html += `
            <button ${this.currentPage === this.totalPages ? 'disabled' : ''} 
                    onclick="CustomersManager.goToPage(${this.currentPage + 1})">
                التالي <i class="fas fa-chevron-left"></i>
            </button>
        `;

        pagination.innerHTML = html;
    },

    // الانتقال لصفحة
    goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            this.loadCustomers();
        }
    },

    // عرض نافذة العميل
    showCustomerModal(customer = null) {
        const modal = document.getElementById('customer-modal');
        const form = document.getElementById('customer-form');
        const title = document.getElementById('modal-title');
        
        if (customer) {
            title.textContent = 'تعديل العميل';
            this.fillCustomerForm(customer);
        } else {
            title.textContent = 'إضافة عميل جديد';
            form.reset();
            document.getElementById('customer-id').value = '';
        }
        
        modal.classList.add('show');
    },

    // ملء نموذج العميل
    fillCustomerForm(customer) {
        document.getElementById('customer-id').value = customer.id;
        document.querySelector('[name="name"]').value = customer.name;
        document.querySelector('[name="phone"]').value = customer.phone || '';
        document.querySelector('[name="address"]').value = customer.address || '';
        document.querySelector('[name="email"]').value = customer.email || '';
        document.querySelector('[name="total_debt"]').value = customer.total_debt || 0;
        document.querySelector('[name="notes"]').value = customer.notes || '';
    },

    // حفظ العميل
    async saveCustomer() {
        try {
            const form = document.getElementById('customer-form');
            const formData = new FormData(form);
            const customerId = document.getElementById('customer-id').value;
            
            // تحويل FormData إلى Object
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            const url = '../api/customers.php';
            const method = customerId ? 'PUT' : 'POST';
            
            if (customerId) {
                data.id = customerId;
            }
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage(result.message, 'success');
                this.hideModals();
                this.loadCustomers();
                this.loadStats();
            } else {
                throw new Error(result.error || 'خطأ في حفظ العميل');
            }
        } catch (error) {
            console.error('Error saving customer:', error);
            showMessage('خطأ في حفظ العميل: ' + error.message, 'error');
        }
    },

    // تعديل عميل
    editCustomer(id) {
        const customer = this.customers.find(c => c.id === id);
        if (customer) {
            this.showCustomerModal(customer);
        }
    },

    // حذف عميل
    async deleteCustomer(id) {
        const confirmed = await confirmDelete('هل أنت متأكد من حذف هذا العميل؟');
        if (!confirmed) return;
        
        try {
            const response = await fetch('../api/customers.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id: id })
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage(result.message, 'success');
                this.loadCustomers();
                this.loadStats();
            } else {
                throw new Error(result.error || 'خطأ في حذف العميل');
            }
        } catch (error) {
            console.error('Error deleting customer:', error);
            showMessage('خطأ في حذف العميل: ' + error.message, 'error');
        }
    },

    // عرض كشف حساب العميل
    async viewStatement(customerId) {
        try {
            const response = await fetch(`../api/customers.php?statement=${customerId}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'خطأ في جلب كشف الحساب');
            }

            const modal = document.getElementById('statement-modal');
            const title = document.getElementById('statement-title');
            const content = document.getElementById('statement-content');
            
            title.textContent = `كشف حساب العميل: ${data.customer.name}`;
            
            content.innerHTML = `
                <div class="statement-summary">
                    <div class="summary-card">
                        <h4>إجمالي المبيعات</h4>
                        <div class="amount">${formatMoney(data.summary.total_sales)}</div>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي المدفوعات</h4>
                        <div class="amount">${formatMoney(data.summary.total_payments)}</div>
                    </div>
                    <div class="summary-card">
                        <h4>الرصيد المتبقي</h4>
                        <div class="amount" style="color: ${data.summary.balance > 0 ? 'var(--danger-color)' : 'var(--success-color)'}">
                            ${formatMoney(data.summary.balance)}
                        </div>
                    </div>
                </div>
                
                <h4>تاريخ المعاملات</h4>
                <table class="statement-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>النوع</th>
                            <th>الوصف</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.generateStatementRows(data.sales, data.payments)}
                    </tbody>
                </table>
            `;
            
            modal.classList.add('show');
            
        } catch (error) {
            console.error('Error loading statement:', error);
            showMessage('خطأ في جلب كشف الحساب: ' + error.message, 'error');
        }
    },

    // إنشاء صفوف كشف الحساب
    generateStatementRows(sales, payments) {
        const transactions = [];
        
        // إضافة المبيعات
        sales.forEach(sale => {
            transactions.push({
                date: sale.created_at,
                type: 'sale',
                description: `فاتورة #${sale.invoice_number}`,
                amount: sale.total
            });
        });
        
        // إضافة المدفوعات
        payments.forEach(payment => {
            transactions.push({
                date: payment.created_at,
                type: 'payment',
                description: `دفعة - ${payment.payment_method}`,
                amount: payment.amount
            });
        });
        
        // ترتيب حسب التاريخ
        transactions.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        return transactions.map(transaction => `
            <tr>
                <td>${formatDateTime(transaction.date)}</td>
                <td>
                    <span class="transaction-type type-${transaction.type}">
                        ${transaction.type === 'sale' ? 'مبيعات' : 'دفعة'}
                    </span>
                </td>
                <td>${transaction.description}</td>
                <td style="color: ${transaction.type === 'sale' ? 'var(--danger-color)' : 'var(--success-color)'}">
                    ${transaction.type === 'sale' ? '+' : '-'}${formatMoney(transaction.amount)}
                </td>
            </tr>
        `).join('');
    },

    // عرض نافذة إضافة دفعة
    showPaymentModal(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;
        
        document.getElementById('payment-customer-id').value = customerId;
        document.querySelector('#payment-form [name="amount"]').value = customer.total_debt;
        
        const modal = document.getElementById('payment-modal');
        modal.classList.add('show');
    },

    // حفظ الدفعة
    async savePayment() {
        try {
            const form = document.getElementById('payment-form');
            const formData = new FormData(form);
            
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            const response = await fetch('../api/customer_payments.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage('تم حفظ الدفعة بنجاح', 'success');
                this.hideModals();
                this.loadCustomers();
                this.loadStats();
                form.reset();
            } else {
                throw new Error(result.error || 'خطأ في حفظ الدفعة');
            }
        } catch (error) {
            console.error('Error saving payment:', error);
            showMessage('خطأ في حفظ الدفعة: ' + error.message, 'error');
        }
    },

    // تصدير إلى Excel
    async exportToExcel() {
        try {
            const response = await fetch('../api/export_customers.php');
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `customers_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showMessage('تم تصدير العملاء بنجاح', 'success');
            } else {
                throw new Error('خطأ في تصدير الملف');
            }
        } catch (error) {
            console.error('Error exporting customers:', error);
            showMessage('خطأ في تصدير الملف: ' + error.message, 'error');
        }
    },

    // استيراد من Excel
    async importFromExcel() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            const formData = new FormData();
            formData.append('excel_file', file);
            
            try {
                const response = await fetch('../api/import_customers.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showMessage(`تم استيراد ${result.imported} عميل بنجاح`, 'success');
                    this.loadCustomers();
                    this.loadStats();
                } else {
                    throw new Error(result.error || 'خطأ في استيراد الملف');
                }
            } catch (error) {
                console.error('Error importing customers:', error);
                showMessage('خطأ في استيراد الملف: ' + error.message, 'error');
            }
        };
        
        input.click();
    },

    // إخفاء النوافذ المنبثقة
    hideModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
        });
    }
};
