<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

try {
    // جلب المنتجات منخفضة المخزون
    $products = fetchAll("
        SELECT id, name, quantity, min_quantity
        FROM products 
        WHERE quantity <= min_quantity 
        AND is_active = 1
        ORDER BY quantity ASC
        LIMIT 10
    ");
    
    echo json_encode($products);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في جلب المنتجات: ' . $e->getMessage()]);
}
?>
