<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

// التحقق من صلاحيات المدير
if ($_SESSION['user_role'] !== 'admin') {
    header('Location: ../admin/dashboard.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة المحل</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/settings.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <span>إدارة المحل</span>
                </div>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="../admin/dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li>
                        <a href="../pos/index.php" class="nav-link">
                            <i class="fas fa-cash-register"></i>
                            <span>نقطة البيع</span>
                        </a>
                    </li>
                    <li>
                        <a href="../products/index.php" class="nav-link">
                            <i class="fas fa-boxes"></i>
                            <span>إدارة المنتجات</span>
                        </a>
                    </li>
                    <li>
                        <a href="../customers/index.php" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>إدارة العملاء</span>
                        </a>
                    </li>
                    <li>
                        <a href="../suppliers/index.php" class="nav-link">
                            <i class="fas fa-truck"></i>
                            <span>إدارة الموردين</span>
                        </a>
                    </li>
                    <li>
                        <a href="../maintenance/index.php" class="nav-link">
                            <i class="fas fa-tools"></i>
                            <span>نظام الصيانة</span>
                        </a>
                    </li>
                    <li>
                        <a href="../reports/index.php" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li>
                        <a href="index.php" class="nav-link active">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                        <span class="user-role"><?php echo $user_role === 'admin' ? 'مدير' : 'موظف'; ?></span>
                    </div>
                </div>
                <a href="../auth/logout.php" class="logout-btn" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>الإعدادات</h1>
                    <div class="breadcrumb">
                        <span>الرئيسية</span>
                        <i class="fas fa-chevron-left"></i>
                        <span>الإعدادات</span>
                    </div>
                </div>

                <div class="top-bar-right">
                    <div class="datetime">
                        <div id="current-time"></div>
                        <div id="current-date"></div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                <!-- Settings Navigation -->
                <div class="settings-nav">
                    <button class="settings-tab active" data-tab="shop">🏪 إعدادات المحل</button>
                    <button class="settings-tab" data-tab="appearance">🎨 المظهر</button>
                    <button class="settings-tab" data-tab="products">📦 المنتجات</button>
                    <button class="settings-tab" data-tab="maintenance">🛠️ الصيانة</button>
                    <button class="settings-tab" data-tab="sales">📦 البيع</button>
                    <button class="settings-tab" data-tab="users">👨‍💻 المستخدمين</button>
                    <button class="settings-tab" data-tab="backup">📊 النسخ الاحتياطي</button>
                    <button class="settings-tab" data-tab="security">🔑 الأمان</button>
                </div>

                <!-- Shop Settings -->
                <div class="settings-section active" id="shop-settings">
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>🏪 إعدادات المحل الأساسية</h3>
                            <p>البيانات الأساسية للمحل التي تظهر في الفواتير والواجهة</p>
                        </div>
                        <div class="card-content">
                            <form id="shop-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>اسم المحل</label>
                                        <input type="text" name="shop_name" value="TechZone - موبايلات وصيانة باحتراف" required>
                                    </div>
                                    <div class="form-group">
                                        <label>شعار المحل</label>
                                        <input type="file" name="shop_logo" accept="image/*">
                                        <small>الحد الأقصى: 2MB - PNG, JPG</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>رقم الهاتف</label>
                                        <input type="tel" name="shop_phone" value="+96477119992716" required>
                                    </div>
                                    <div class="form-group">
                                        <label>رقم الواتساب</label>
                                        <input type="tel" name="shop_whatsapp" value="+96477119992716" required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>عنوان المحل</label>
                                    <textarea name="shop_address" rows="3" required>البصرة - شط العرب - باب الهوى مقابل شارع رقم 10</textarea>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>البريد الإلكتروني</label>
                                        <input type="email" name="shop_email" value="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label>أوقات الدوام</label>
                                        <input type="text" name="working_hours" value="السبت - الخميس: 9:00 ص - 10:00 م">
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>🌐 روابط التواصل الاجتماعي</h4>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>فيسبوك</label>
                                            <input type="url" name="facebook_url" placeholder="https://facebook.com/yourpage">
                                        </div>
                                        <div class="form-group">
                                            <label>إنستغرام</label>
                                            <input type="url" name="instagram_url" placeholder="https://instagram.com/yourpage">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ التعديلات
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetForm('shop-form')">
                                        <i class="fas fa-undo"></i> إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Appearance Settings -->
                <div class="settings-section" id="appearance-settings">
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>🎨 إعدادات المظهر والتصميم</h3>
                            <p>تخصيص شكل ومظهر الموقع والواجهة</p>
                        </div>
                        <div class="card-content">
                            <form id="appearance-form">
                                <div class="form-section">
                                    <h4>🎨 الألوان الأساسية</h4>
                                    <div class="color-picker-grid">
                                        <div class="color-picker-item">
                                            <label>اللون الأساسي</label>
                                            <input type="color" name="primary_color" value="#0057D9">
                                        </div>
                                        <div class="color-picker-item">
                                            <label>اللون الثانوي</label>
                                            <input type="color" name="secondary_color" value="#28a745">
                                        </div>
                                        <div class="color-picker-item">
                                            <label>لون الخطر</label>
                                            <input type="color" name="danger_color" value="#dc3545">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>🌙 الوضع الليلي</h4>
                                    <div class="toggle-switch">
                                        <input type="checkbox" name="dark_mode" id="dark_mode">
                                        <label for="dark_mode">تفعيل الوضع الليلي</label>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>🖼️ الصور والخلفيات</h4>
                                    <div class="form-group">
                                        <label>صورة الخلفية الرئيسية</label>
                                        <input type="file" name="hero_image" accept="image/*">
                                        <small>الحد الأقصى: 5MB - JPG, PNG</small>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ التعديلات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Products Settings -->
                <div class="settings-section" id="products-settings">
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>📦 إعدادات المنتجات</h3>
                            <p>إدارة إعدادات المنتجات والمخزون</p>
                        </div>
                        <div class="card-content">
                            <form id="products-form">
                                <div class="form-section">
                                    <h4>📊 إعدادات المخزون</h4>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>الحد الأدنى للمخزون</label>
                                            <input type="number" name="min_stock" value="5" min="1">
                                            <small>سيتم إرسال تنبيه عند انخفاض الكمية</small>
                                        </div>
                                        <div class="form-group">
                                            <label>عرض المنتجات المنتهية</label>
                                            <select name="show_out_of_stock">
                                                <option value="1">عرض مع تنبيه</option>
                                                <option value="0">إخفاء تماماً</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>📂 التصنيفات</h4>
                                    <div class="categories-manager">
                                        <div class="category-item">
                                            <input type="text" value="موبايلات" readonly>
                                            <button type="button" class="btn-small btn-danger">حذف</button>
                                        </div>
                                        <div class="category-item">
                                            <input type="text" value="اكسسوارات" readonly>
                                            <button type="button" class="btn-small btn-danger">حذف</button>
                                        </div>
                                        <div class="category-item">
                                            <input type="text" value="قطع غيار" readonly>
                                            <button type="button" class="btn-small btn-danger">حذف</button>
                                        </div>
                                        <div class="add-category">
                                            <input type="text" placeholder="تصنيف جديد">
                                            <button type="button" class="btn-small btn-primary">إضافة</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>📤 استيراد المنتجات</h4>
                                    <div class="import-section">
                                        <button type="button" class="btn btn-secondary" onclick="downloadTemplate()">
                                            <i class="fas fa-download"></i> تحميل قالب Excel
                                        </button>
                                        <input type="file" name="products_excel" accept=".xlsx,.xls">
                                        <button type="button" class="btn btn-primary" onclick="importProducts()">
                                            <i class="fas fa-upload"></i> استيراد المنتجات
                                        </button>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ التعديلات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Other sections will be added in the next part -->
                <div class="settings-section" id="maintenance-settings">
                    <div class="coming-soon">
                        <h3>🛠️ إعدادات الصيانة</h3>
                        <p>قيد التطوير...</p>
                    </div>
                </div>

                <div class="settings-section" id="sales-settings">
                    <div class="coming-soon">
                        <h3>📦 إعدادات البيع</h3>
                        <p>قيد التطوير...</p>
                    </div>
                </div>

                <div class="settings-section" id="users-settings">
                    <div class="coming-soon">
                        <h3>👨‍💻 إدارة المستخدمين</h3>
                        <p>قيد التطوير...</p>
                    </div>
                </div>

                <div class="settings-section" id="backup-settings">
                    <div class="coming-soon">
                        <h3>📊 النسخ الاحتياطي</h3>
                        <p>قيد التطوير...</p>
                    </div>
                </div>

                <div class="settings-section" id="security-settings">
                    <div class="coming-soon">
                        <h3>🔑 الأمان والحماية</h3>
                        <p>قيد التطوير...</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        // إدارة التبويبات
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // إزالة الفئة النشطة من جميع التبويبات
                document.querySelectorAll('.settings-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.settings-section').forEach(s => s.classList.remove('active'));

                // إضافة الفئة النشطة للتبويب المحدد
                tab.classList.add('active');
                const targetSection = document.getElementById(tab.dataset.tab + '-settings');
                if (targetSection) {
                    targetSection.classList.add('active');
                }
            });
        });

        // حفظ إعدادات المحل
        document.getElementById('shop-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);

            try {
                const response = await fetch('../api/save_settings.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('تم حفظ الإعدادات بنجاح', 'success');
                } else {
                    showMessage('خطأ في حفظ الإعدادات: ' + result.error, 'error');
                }
            } catch (error) {
                showMessage('خطأ في الاتصال بالخادم', 'error');
            }
        });

        // إعادة تعيين النموذج
        function resetForm(formId) {
            document.getElementById(formId).reset();
        }

        // تحميل قالب Excel
        function downloadTemplate() {
            window.open('../api/download_template.php', '_blank');
        }

        // استيراد المنتجات
        function importProducts() {
            const fileInput = document.querySelector('input[name="products_excel"]');
            if (!fileInput.files[0]) {
                showMessage('يرجى اختيار ملف Excel أولاً', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('excel_file', fileInput.files[0]);

            fetch('../api/import_products.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showMessage('تم استيراد المنتجات بنجاح', 'success');
                } else {
                    showMessage('خطأ في استيراد المنتجات: ' + result.error, 'error');
                }
            })
            .catch(error => {
                showMessage('خطأ في الاتصال بالخادم', 'error');
            });
        }
    </script>
</body>
</html>
