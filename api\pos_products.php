<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'GET') {
        $search = $_GET['search'] ?? '';
        $category = $_GET['category'] ?? '';
        $barcode = $_GET['barcode'] ?? '';
        
        $sql = "
            SELECT id, name, barcode, category, selling_price, quantity, image
            FROM products 
            WHERE is_active = 1
        ";
        $params = [];
        
        if (!empty($search)) {
            $sql .= " AND (name LIKE ? OR barcode LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($category)) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }
        
        if (!empty($barcode)) {
            $sql .= " AND barcode = ?";
            $params[] = $barcode;
        }
        
        $sql .= " ORDER BY name ASC LIMIT 50";
        
        $products = fetchAll($sql, $params) ?: [];
        
        echo json_encode($products);
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'طريقة غير مدعومة']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}
?>
