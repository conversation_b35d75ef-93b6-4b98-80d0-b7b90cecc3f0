<?php
require_once 'config/database.php';

echo "<h2>تحديث قاعدة البيانات</h2>";

try {
    // جدول المبيعات
    $pdo->exec("CREATE TABLE IF NOT EXISTS sales (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        customer_id INT NULL,
        customer_name VARCHAR(100),
        total DECIMAL(10,2) NOT NULL,
        discount DECIMAL(10,2) DEFAULT 0,
        payment_method ENUM('cash', 'credit') DEFAULT 'cash',
        notes TEXT,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
        FOREIGN KEY (user_id) REFERENCES users(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ تم إنشاء جدول المبيعات<br>";

    // جدول تفاصيل المبيعات
    $pdo->exec("CREATE TABLE IF NOT EXISTS sale_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sale_id INT NOT NULL,
        product_id INT NOT NULL,
        product_name VARCHAR(200) NOT NULL,
        quantity INT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        total DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ تم إنشاء جدول تفاصيل المبيعات<br>";

    // جدول الصيانة
    $pdo->exec("CREATE TABLE IF NOT EXISTS maintenance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT NULL,
        customer_name VARCHAR(100) NOT NULL,
        customer_phone VARCHAR(20),
        device_type VARCHAR(100) NOT NULL,
        device_model VARCHAR(100),
        problem_description TEXT NOT NULL,
        estimated_cost DECIMAL(10,2) DEFAULT 0,
        actual_cost DECIMAL(10,2) DEFAULT 0,
        status ENUM('received', 'in_progress', 'ready', 'delivered') DEFAULT 'received',
        notes TEXT,
        technician_notes TEXT,
        received_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        delivery_date TIMESTAMP NULL,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
        FOREIGN KEY (user_id) REFERENCES users(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ تم إنشاء جدول الصيانة<br>";

    // جدول قطع الغيار المستخدمة في الصيانة
    $pdo->exec("CREATE TABLE IF NOT EXISTS maintenance_parts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        maintenance_id INT NOT NULL,
        product_id INT NOT NULL,
        product_name VARCHAR(200) NOT NULL,
        quantity INT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        total DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (maintenance_id) REFERENCES maintenance(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ تم إنشاء جدول قطع الغيار للصيانة<br>";

    // جدول مدفوعات العملاء
    $pdo->exec("CREATE TABLE IF NOT EXISTS customer_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('cash', 'bank_transfer', 'check') DEFAULT 'cash',
        notes TEXT,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ تم إنشاء جدول مدفوعات العملاء<br>";

    // جدول رسائل التواصل
    $pdo->exec("CREATE TABLE IF NOT EXISTS contact_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        subject VARCHAR(200),
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ تم إنشاء جدول رسائل التواصل<br>";

    // جدول طلبات الصيانة من الموقع
    $pdo->exec("CREATE TABLE IF NOT EXISTS maintenance_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_name VARCHAR(100) NOT NULL,
        customer_phone VARCHAR(20) NOT NULL,
        device_type VARCHAR(100) NOT NULL,
        device_model VARCHAR(100),
        problem_description TEXT NOT NULL,
        status ENUM('pending', 'contacted', 'scheduled', 'received') DEFAULT 'pending',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ تم إنشاء جدول طلبات الصيانة<br>";

    // جدول سجل الأنشطة
    $pdo->exec("CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        action VARCHAR(100) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    echo "✓ تم إنشاء جدول سجل الأنشطة<br>";

    // إنشاء المستخدم الافتراضي
    $adminExists = fetchOne("SELECT id FROM users WHERE username = 'admin'");
    if (!$adminExists) {
        $adminPassword = hashPassword('password');
        executeQuery("INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)", 
                    ['admin', $adminPassword, 'المدير العام', 'admin']);
        echo "✓ تم إنشاء المستخدم الافتراضي (admin/password)<br>";
    } else {
        echo "✓ المستخدم الافتراضي موجود مسبقاً<br>";
    }

    // إضافة بيانات تجريبية للمنتجات
    $productExists = fetchOne("SELECT id FROM products LIMIT 1");
    if (!$productExists) {
        $sampleProducts = [
            ['iPhone 13', '1234567890123', 'mobile', 'هاتف ذكي من آبل', 800000, 900000, 10, 2],
            ['سماعة بلوتوث', '1234567890124', 'accessories', 'سماعة لاسلكية عالية الجودة', 50000, 70000, 25, 5],
            ['شاشة iPhone 12', '1234567890125', 'parts', 'شاشة أصلية لآيفون 12', 150000, 200000, 5, 1],
            ['كفر حماية', '1234567890126', 'accessories', 'كفر حماية شفاف', 5000, 10000, 50, 10],
            ['بطارية Samsung', '1234567890127', 'parts', 'بطارية أصلية لسامسونج', 30000, 50000, 15, 3]
        ];

        foreach ($sampleProducts as $product) {
            executeQuery("INSERT INTO products (name, barcode, category, description, purchase_price, selling_price, quantity, min_quantity) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", $product);
        }
        echo "✓ تم إضافة منتجات تجريبية<br>";
    }

    // إضافة عملاء تجريبيين
    $customerExists = fetchOne("SELECT id FROM customers LIMIT 1");
    if (!$customerExists) {
        $sampleCustomers = [
            ['أحمد محمد', '07901234567', 'بغداد - الكرادة', '<EMAIL>', 0],
            ['فاطمة علي', '07907654321', 'بغداد - المنصور', '<EMAIL>', 50000],
            ['محمد حسن', '07905555555', 'بغداد - الجادرية', '<EMAIL>', 0]
        ];

        foreach ($sampleCustomers as $customer) {
            executeQuery("INSERT INTO customers (name, phone, address, email, total_debt) VALUES (?, ?, ?, ?, ?)", $customer);
        }
        echo "✓ تم إضافة عملاء تجريبيين<br>";
    }

    echo "<br><h3 style='color: green;'>✅ تم تحديث قاعدة البيانات بنجاح!</h3>";
    echo "<p><a href='auth/login.php'>انتقل لصفحة تسجيل الدخول</a></p>";
    echo "<p><strong>بيانات الدخول الافتراضية:</strong><br>";
    echo "اسم المستخدم: admin<br>";
    echo "كلمة المرور: password</p>";

} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ خطأ في تحديث قاعدة البيانات:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
