<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost();
            break;
        case 'PUT':
            handlePut();
            break;
        case 'DELETE':
            handleDelete();
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'طريقة غير مدعومة']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

function handleGet() {
    global $pdo;
    
    if (isset($_GET['id'])) {
        // جلب منتج واحد
        $id = (int)$_GET['id'];
        $product = fetchOne("SELECT * FROM products WHERE id = ? AND is_active = 1", [$id]);
        
        if ($product) {
            echo json_encode($product);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'المنتج غير موجود']);
        }
    } else {
        // جلب جميع المنتجات
        $search = $_GET['search'] ?? '';
        $category = $_GET['category'] ?? '';
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 50);
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT * FROM products WHERE is_active = 1";
        $params = [];
        
        if (!empty($search)) {
            $sql .= " AND (name LIKE ? OR barcode LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if (!empty($category)) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }
        
        $sql .= " ORDER BY name LIMIT $limit OFFSET $offset";
        
        $products = fetchAll($sql, $params);
        
        // حساب العدد الكلي
        $countSql = "SELECT COUNT(*) as total FROM products WHERE is_active = 1";
        $countParams = [];
        
        if (!empty($search)) {
            $countSql .= " AND (name LIKE ? OR barcode LIKE ?)";
            $countParams[] = "%$search%";
            $countParams[] = "%$search%";
        }
        
        if (!empty($category)) {
            $countSql .= " AND category = ?";
            $countParams[] = $category;
        }
        
        $total = fetchOne($countSql, $countParams)['total'];
        
        echo json_encode([
            'products' => $products,
            'total' => $total,
            'page' => $page,
            'pages' => ceil($total / $limit)
        ]);
    }
}

function handlePost() {
    global $pdo;
    
    // التحقق من الصلاحيات
    if ($_SESSION['user_role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'غير مصرح لك بهذه العملية']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    $required = ['name', 'category', 'selling_price'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "الحقل $field مطلوب"]);
            return;
        }
    }
    
    // إنشاء باركود إذا لم يتم توفيره
    if (empty($input['barcode'])) {
        $input['barcode'] = generateRandomCode(12);
    }
    
    // التحقق من عدم تكرار الباركود
    $existing = fetchOne("SELECT id FROM products WHERE barcode = ?", [$input['barcode']]);
    if ($existing) {
        http_response_code(400);
        echo json_encode(['error' => 'الباركود موجود مسبقاً']);
        return;
    }
    
    $sql = "INSERT INTO products (name, barcode, category, description, purchase_price, selling_price, quantity, min_quantity, image) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        sanitize($input['name']),
        sanitize($input['barcode']),
        sanitize($input['category']),
        sanitize($input['description'] ?? ''),
        (float)($input['purchase_price'] ?? 0),
        (float)$input['selling_price'],
        (int)($input['quantity'] ?? 0),
        (int)($input['min_quantity'] ?? 5),
        sanitize($input['image'] ?? '')
    ];
    
    if (executeQuery($sql, $params)) {
        $productId = getLastInsertId();
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة المنتج بنجاح',
            'product_id' => $productId
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في إضافة المنتج']);
    }
}

function handlePut() {
    global $pdo;
    
    // التحقق من الصلاحيات
    if ($_SESSION['user_role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'غير مصرح لك بهذه العملية']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف المنتج مطلوب']);
        return;
    }
    
    $id = (int)$input['id'];
    
    // التحقق من وجود المنتج
    $product = fetchOne("SELECT * FROM products WHERE id = ?", [$id]);
    if (!$product) {
        http_response_code(404);
        echo json_encode(['error' => 'المنتج غير موجود']);
        return;
    }
    
    // التحقق من عدم تكرار الباركود
    if (!empty($input['barcode']) && $input['barcode'] !== $product['barcode']) {
        $existing = fetchOne("SELECT id FROM products WHERE barcode = ? AND id != ?", [$input['barcode'], $id]);
        if ($existing) {
            http_response_code(400);
            echo json_encode(['error' => 'الباركود موجود مسبقاً']);
            return;
        }
    }
    
    $sql = "UPDATE products SET 
            name = ?, 
            barcode = ?, 
            category = ?, 
            description = ?, 
            purchase_price = ?, 
            selling_price = ?, 
            quantity = ?, 
            min_quantity = ?, 
            image = ?,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";
    
    $params = [
        sanitize($input['name'] ?? $product['name']),
        sanitize($input['barcode'] ?? $product['barcode']),
        sanitize($input['category'] ?? $product['category']),
        sanitize($input['description'] ?? $product['description']),
        (float)($input['purchase_price'] ?? $product['purchase_price']),
        (float)($input['selling_price'] ?? $product['selling_price']),
        (int)($input['quantity'] ?? $product['quantity']),
        (int)($input['min_quantity'] ?? $product['min_quantity']),
        sanitize($input['image'] ?? $product['image']),
        $id
    ];
    
    if (executeQuery($sql, $params)) {
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث المنتج بنجاح'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في تحديث المنتج']);
    }
}

function handleDelete() {
    global $pdo;
    
    // التحقق من الصلاحيات
    if ($_SESSION['user_role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'غير مصرح لك بهذه العملية']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف المنتج مطلوب']);
        return;
    }
    
    $id = (int)$input['id'];
    
    // التحقق من وجود المنتج
    $product = fetchOne("SELECT * FROM products WHERE id = ?", [$id]);
    if (!$product) {
        http_response_code(404);
        echo json_encode(['error' => 'المنتج غير موجود']);
        return;
    }
    
    // حذف منطقي (تعطيل المنتج)
    if (executeQuery("UPDATE products SET is_active = 0 WHERE id = ?", [$id])) {
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف المنتج بنجاح'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في حذف المنتج']);
    }
}
?>
