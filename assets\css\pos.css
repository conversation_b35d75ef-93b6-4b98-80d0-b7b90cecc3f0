/* نقطة البيع - POS */
.pos-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.pos-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: var(--white);
    padding: 15px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left h1 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.cashier {
    font-size: 0.9rem;
    opacity: 0.9;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.datetime {
    text-align: center;
    font-size: 0.9rem;
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: 8px 15px;
    border-radius: 6px;
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* تخطيط نقطة البيع */
.pos-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 20px;
    padding: 20px;
    flex: 1;
    overflow: hidden;
}

/* قسم المنتجات */
.products-section {
    display: flex;
    flex-direction: column;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.search-section {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
}

.search-box {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.search-box input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.scan-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.scan-btn:hover {
    background: #1d4ed8;
}

.category-filter {
    display: flex;
    gap: 10px;
}

.filter-btn {
    background: var(--gray-100);
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.products-grid {
    flex: 1;
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    overflow-y: auto;
}

.product-card {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.product-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-card img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 10px;
}

.product-card .name {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.product-card .price {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1rem;
}

.product-card .stock {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin-top: 5px;
}

/* قسم الفاتورة */
.invoice-section {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.invoice-header {
    background: var(--gray-50);
    padding: 15px 20px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.invoice-header h2 {
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.invoice-number {
    font-size: 0.9rem;
    color: var(--gray-600);
}

.customer-section {
    padding: 15px 20px;
    border-bottom: 1px solid var(--gray-200);
}

.customer-select {
    display: flex;
    gap: 10px;
}

.customer-select select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
}

.add-btn {
    background: var(--success-color);
    color: var(--white);
    border: none;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.invoice-items {
    flex: 1;
    overflow-y: auto;
}

.items-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 60px;
    gap: 10px;
    padding: 10px 20px;
    background: var(--gray-50);
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 1px solid var(--gray-200);
}

.items-list {
    min-height: 200px;
}

.invoice-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 60px;
    gap: 10px;
    padding: 15px 20px;
    border-bottom: 1px solid var(--gray-100);
    align-items: center;
}

.item-name {
    font-weight: 500;
}

.quantity-input {
    width: 60px;
    padding: 5px;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
    text-align: center;
}

.remove-item {
    background: var(--danger-color);
    color: var(--white);
    border: none;
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
    width: 30px;
    height: 30px;
}

.invoice-summary {
    padding: 20px;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 1rem;
}

.discount-row .discount-input {
    display: flex;
    gap: 5px;
}

.discount-input input {
    width: 80px;
    padding: 5px;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
}

.discount-input select {
    padding: 5px;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
}

.total-row {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    border-top: 2px solid var(--gray-300);
    padding-top: 10px;
}

.payment-section {
    padding: 20px;
    border-top: 1px solid var(--gray-200);
}

.payment-type {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.payment-type label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.payment-amount {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.amount-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.amount-row input {
    width: 120px;
    padding: 8px;
    border: 1px solid var(--gray-300);
    border-radius: 4px;
    text-align: center;
}

.action-buttons {
    padding: 20px;
    display: flex;
    gap: 10px;
    border-top: 1px solid var(--gray-200);
}

.btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-secondary {
    background: var(--gray-500);
    color: var(--white);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-500);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
}

.form-actions {
    padding: 20px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    border-top: 1px solid var(--gray-200);
}

/* Responsive */
@media (max-width: 1024px) {
    .pos-layout {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }
    
    .invoice-section {
        max-height: 50vh;
    }
}

@media (max-width: 768px) {
    .pos-header .header-content {
        flex-direction: column;
        gap: 10px;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .items-header,
    .invoice-item {
        grid-template-columns: 2fr 80px 80px 80px 40px;
        font-size: 0.8rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
