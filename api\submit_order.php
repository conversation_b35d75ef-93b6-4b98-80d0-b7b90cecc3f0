<?php
header('Content-Type: application/json; charset=utf-8');
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مدعومة']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (empty($input['customer_name']) || empty($input['customer_phone']) || 
        empty($input['customer_address']) || empty($input['items'])) {
        http_response_code(400);
        echo json_encode(['error' => 'البيانات المطلوبة ناقصة']);
        exit();
    }
    
    // إنشاء جدول الطلبات إذا لم يكن موجوداً
    $createOrdersTable = "
        CREATE TABLE IF NOT EXISTS customer_orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_number VARCHAR(50) UNIQUE NOT NULL,
            customer_name VARCHAR(100) NOT NULL,
            customer_phone VARCHAR(20) NOT NULL,
            customer_address TEXT NOT NULL,
            delivery_method ENUM('pickup', 'delivery') DEFAULT 'pickup',
            delivery_cost DECIMAL(10,2) DEFAULT 0,
            subtotal DECIMAL(10,2) NOT NULL,
            total DECIMAL(10,2) NOT NULL,
            status ENUM('pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled') DEFAULT 'pending',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    
    executeQuery($createOrdersTable);
    
    // إنشاء جدول تفاصيل الطلبات
    $createOrderItemsTable = "
        CREATE TABLE IF NOT EXISTS customer_order_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            product_id INT,
            product_name VARCHAR(200) NOT NULL,
            quantity INT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            total DECIMAL(10,2) NOT NULL,
            FOREIGN KEY (order_id) REFERENCES customer_orders(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    
    executeQuery($createOrderItemsTable);
    
    // إنشاء رقم طلب فريد
    $orderNumber = 'ORD' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // بدء المعاملة
    $pdo->beginTransaction();
    
    // إدراج الطلب
    $orderSql = "INSERT INTO customer_orders 
                 (order_number, customer_name, customer_phone, customer_address, 
                  delivery_method, delivery_cost, subtotal, total, notes) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $orderParams = [
        $orderNumber,
        sanitize($input['customer_name']),
        sanitize($input['customer_phone']),
        sanitize($input['customer_address']),
        $input['delivery_method'] ?? 'pickup',
        (float)($input['delivery_cost'] ?? 0),
        (float)$input['total'],
        (float)$input['final_total'],
        sanitize($input['notes'] ?? '')
    ];
    
    if (!executeQuery($orderSql, $orderParams)) {
        throw new Exception('خطأ في حفظ الطلب');
    }
    
    $orderId = getLastInsertId();
    
    // إدراج تفاصيل الطلب
    $itemSql = "INSERT INTO customer_order_items 
                (order_id, product_id, product_name, quantity, price, total) 
                VALUES (?, ?, ?, ?, ?, ?)";
    
    foreach ($input['items'] as $item) {
        $itemParams = [
            $orderId,
            (int)$item['id'],
            sanitize($item['name']),
            (int)$item['quantity'],
            (float)$item['price'],
            (float)$item['total']
        ];
        
        if (!executeQuery($itemSql, $itemParams)) {
            throw new Exception('خطأ في حفظ تفاصيل الطلب');
        }
    }
    
    // تأكيد المعاملة
    $pdo->commit();
    
    // إرسال إشعار (اختياري)
    try {
        // يمكن إضافة إرسال إشعار واتساب أو إيميل هنا
        logActivity('customer_order_created', "طلب جديد من العميل: {$input['customer_name']}", null);
    } catch (Exception $e) {
        // تجاهل أخطاء الإشعارات
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'تم إرسال طلبك بنجاح',
        'order_id' => $orderId,
        'order_number' => $orderNumber
    ]);
    
} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في إرسال الطلب: ' . $e->getMessage()]);
}
?>
