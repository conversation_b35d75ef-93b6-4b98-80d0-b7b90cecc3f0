/* صفحة تسجيل الدخول */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --white: #ffffff;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    --border-radius: 12px;
    --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.login-container {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 450px;
}

.login-box {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: var(--white);
    padding: 40px 30px;
    text-align: center;
    position: relative;
}

.login-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: var(--white);
    border-radius: 50% 50% 0 0 / 100% 100% 0 0;
}

.login-header i {
    font-size: 4rem;
    margin-bottom: 15px;
    color: #fbbf24;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.login-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.login-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.error-message {
    background: var(--danger-color);
    color: var(--white);
    padding: 15px 30px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.error-message i {
    font-size: 1.2rem;
}

.login-form {
    padding: 40px 30px;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
    font-size: 1rem;
}

.form-group label i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.form-group input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
    background: var(--gray-50);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group.focused label {
    color: var(--primary-color);
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: var(--white);
    border: none;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: inherit;
    position: relative;
    overflow: hidden;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-footer {
    background: var(--gray-50);
    padding: 25px 30px;
    text-align: center;
    border-top: 1px solid var(--gray-200);
}

.login-footer p {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 5px;
}

.login-footer small {
    color: var(--gray-500);
    font-size: 0.9rem;
}

/* تأثيرات إضافية */
.form-group input::placeholder {
    color: var(--gray-400);
}

.form-group input:invalid {
    border-color: var(--danger-color);
}

.form-group input:valid {
    border-color: var(--success-color);
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        max-width: 100%;
    }
    
    .login-header {
        padding: 30px 20px;
    }
    
    .login-header i {
        font-size: 3rem;
    }
    
    .login-header h1 {
        font-size: 1.5rem;
    }
    
    .login-form {
        padding: 30px 20px;
    }
    
    .login-footer {
        padding: 20px;
    }
    
    .form-group input {
        padding: 12px 15px;
    }
    
    .login-btn {
        padding: 12px 15px;
    }
}

/* تأثير التحميل */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تأثيرات الحركة */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
