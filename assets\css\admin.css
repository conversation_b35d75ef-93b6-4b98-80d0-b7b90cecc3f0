/* لوحة التحكم الإدارية */

/* تخطيط الإدارة */
.admin-layout {
    display: flex;
    min-height: 100vh;
    background: #f8fafc;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: var(--white);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar .logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.3rem;
    font-weight: 700;
}

.sidebar .logo i {
    font-size: 1.8rem;
    color: #fbbf24;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 0;
    position: relative;
}

.sidebar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

.sidebar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: var(--white);
    border-left: 4px solid #fbbf24;
}

.sidebar-nav .nav-link i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed .nav-link span {
    display: none;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.7;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border: none;
    padding: 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logout-btn:hover {
    background: var(--danger-color);
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
}

.sidebar.collapsed + .main-content {
    margin-right: 70px;
}

/* الشريط العلوي */
.top-bar {
    background: var(--white);
    padding: 20px 30px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.top-bar-left h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--gray-500);
    font-size: 0.9rem;
}

.breadcrumb i {
    font-size: 0.7rem;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.datetime {
    text-align: center;
    color: var(--gray-600);
    font-size: 0.9rem;
}

.view-site-btn {
    background: var(--primary-color);
    color: var(--white);
    padding: 10px 20px;
    border-radius: var(--border-radius);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    transition: var(--transition);
}

.view-site-btn:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
}

/* محتوى لوحة التحكم */
.dashboard-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card.sales {
    border-left-color: var(--success-color);
}

.stat-card.orders {
    border-left-color: var(--primary-color);
}

.stat-card.maintenance {
    border-left-color: var(--warning-color);
}

.stat-card.inventory {
    border-left-color: var(--danger-color);
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--white);
}

.stat-card.sales .stat-icon {
    background: var(--success-color);
}

.stat-card.orders .stat-icon {
    background: var(--primary-color);
}

.stat-card.maintenance .stat-icon {
    background: var(--warning-color);
}

.stat-card.inventory .stat-icon {
    background: var(--danger-color);
}

.stat-info h3 {
    font-size: 1rem;
    color: var(--gray-600);
    margin-bottom: 8px;
    font-weight: 600;
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

.stat-change.neutral {
    color: var(--gray-500);
}

/* شبكة لوحة التحكم */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8fafc;
}

.card-header h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h3 i {
    color: var(--primary-color);
}

.card-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: var(--transition);
}

.btn-small:hover {
    background: #1d4ed8;
}

.card-content {
    padding: 25px;
}

/* الفواتير الأخيرة */
.recent-orders {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.order-info h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.order-info p {
    font-size: 0.8rem;
    color: var(--gray-500);
}

.order-amount {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--success-color);
}

/* المنتجات منخفضة المخزون */
.low-stock-products {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stock-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #fef2f2;
    border-radius: 8px;
    border-left: 3px solid var(--danger-color);
}

.stock-item h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--dark-color);
}

.stock-quantity {
    background: var(--danger-color);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* حالة الصيانة */
.maintenance-status {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.maintenance-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f0f9ff;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.maintenance-item h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--dark-color);
}

.maintenance-status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-received {
    background: #dbeafe;
    color: #1d4ed8;
}

.status-progress {
    background: #fef3c7;
    color: #d97706;
}

.status-ready {
    background: #dcfce7;
    color: #16a34a;
}

/* الإجراءات السريعة */
.quick-actions {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.quick-actions h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.quick-actions h3 i {
    color: var(--warning-color);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    text-align: center;
}

.action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-3px);
}

.action-btn i {
    font-size: 2rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.action-btn:hover i {
    color: var(--white);
}

.action-btn span {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
}

@media (max-width: 768px) {
    .top-bar {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .dashboard-content {
        padding: 20px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
}
