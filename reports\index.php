<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام إدارة المحل</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/coming-soon.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <span>إدارة المحل</span>
                </div>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="../admin/dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li>
                        <a href="../pos/index.php" class="nav-link">
                            <i class="fas fa-cash-register"></i>
                            <span>نقطة البيع</span>
                        </a>
                    </li>
                    <li>
                        <a href="../products/index.php" class="nav-link">
                            <i class="fas fa-boxes"></i>
                            <span>إدارة المنتجات</span>
                        </a>
                    </li>
                    <li>
                        <a href="../customers/index.php" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>إدارة العملاء</span>
                        </a>
                    </li>
                    <li>
                        <a href="../suppliers/index.php" class="nav-link">
                            <i class="fas fa-truck"></i>
                            <span>إدارة الموردين</span>
                        </a>
                    </li>
                    <li>
                        <a href="../maintenance/index.php" class="nav-link">
                            <i class="fas fa-tools"></i>
                            <span>نظام الصيانة</span>
                        </a>
                    </li>
                    <li>
                        <a href="index.php" class="nav-link active">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <?php if ($user_role === 'admin'): ?>
                    <li>
                        <a href="../settings/index.php" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                        <span class="user-role"><?php echo $user_role === 'admin' ? 'مدير' : 'موظف'; ?></span>
                    </div>
                </div>
                <a href="../auth/logout.php" class="logout-btn" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>التقارير</h1>
                    <div class="breadcrumb">
                        <span>الرئيسية</span>
                        <i class="fas fa-chevron-left"></i>
                        <span>التقارير</span>
                    </div>
                </div>

                <div class="top-bar-right">
                    <div class="datetime">
                        <div id="current-time"></div>
                        <div id="current-date"></div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                <div class="coming-soon">
                    <div class="coming-soon-content">
                        <i class="fas fa-chart-bar"></i>
                        <h2>التقارير والإحصائيات</h2>
                        <p>هذه الصفحة قيد التطوير وستكون متاحة قريباً</p>
                        <div class="features-list">
                            <ul>
                                <li><i class="fas fa-check"></i> تقارير المبيعات اليومية والشهرية</li>
                                <li><i class="fas fa-check"></i> تقارير الأرباح والخسائر</li>
                                <li><i class="fas fa-check"></i> تقارير المخزون</li>
                                <li><i class="fas fa-check"></i> تقارير الصيانة</li>
                                <li><i class="fas fa-check"></i> تقارير العملاء والديون</li>
                            </ul>
                        </div>
                        <a href="../admin/dashboard.php" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
