<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

try {
    // مبيعات اليوم
    $todaySales = fetchOne("
        SELECT COALESCE(SUM(total), 0) as total 
        FROM sales 
        WHERE DATE(created_at) = CURDATE()
    ")['total'];
    
    // عدد فواتير اليوم
    $todayInvoices = fetchOne("
        SELECT COUNT(*) as count 
        FROM sales 
        WHERE DATE(created_at) = CURDATE()
    ")['count'];
    
    // أجهزة قيد الصيانة
    $maintenanceCount = fetchOne("
        SELECT COUNT(*) as count 
        FROM maintenance 
        WHERE status IN ('received', 'in_progress', 'ready')
    ")['count'];
    
    // منتجات منخفضة المخزون
    $lowStock = fetchOne("
        SELECT COUNT(*) as count 
        FROM products 
        WHERE quantity <= min_quantity AND is_active = 1
    ")['count'];
    
    echo json_encode([
        'today_sales' => number_format($todaySales, 0, '.', ','),
        'today_invoices' => $todayInvoices,
        'maintenance_count' => $maintenanceCount,
        'low_stock' => $lowStock
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في جلب الإحصائيات: ' . $e->getMessage()]);
}
?>
