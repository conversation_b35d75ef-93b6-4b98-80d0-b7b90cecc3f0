<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

try {
    // أجهزة مستلمة
    $received = fetchOne("
        SELECT COUNT(*) as count 
        FROM maintenance 
        WHERE status = 'received'
    ")['count'];
    
    // أجهزة قيد التصليح
    $in_progress = fetchOne("
        SELECT COUNT(*) as count 
        FROM maintenance 
        WHERE status = 'in_progress'
    ")['count'];
    
    // أجهزة جاهزة للتسليم
    $ready = fetchOne("
        SELECT COUNT(*) as count 
        FROM maintenance 
        WHERE status = 'ready'
    ")['count'];
    
    // أجهزة مسلمة
    $delivered = fetchOne("
        SELECT COUNT(*) as count 
        FROM maintenance 
        WHERE status = 'delivered'
    ")['count'];
    
    echo json_encode([
        'received' => $received,
        'in_progress' => $in_progress,
        'ready' => $ready,
        'delivered' => $delivered
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في جلب الإحصائيات: ' . $e->getMessage()]);
}
?>
