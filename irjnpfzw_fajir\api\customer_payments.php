<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost();
            break;
        case 'DELETE':
            handleDelete();
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'طريقة غير مدعومة']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

function handleGet() {
    global $pdo;
    
    if (isset($_GET['customer_id'])) {
        // جلب مدفوعات عميل معين
        $customerId = (int)$_GET['customer_id'];
        
        $payments = fetchAll("
            SELECT p.*, u.full_name as user_name
            FROM customer_payments p
            LEFT JOIN users u ON p.user_id = u.id
            WHERE p.customer_id = ?
            ORDER BY p.created_at DESC
        ", [$customerId]);
        
        echo json_encode($payments);
    } else {
        // جلب جميع المدفوعات
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 50);
        $offset = ($page - 1) * $limit;
        
        $payments = fetchAll("
            SELECT p.*, c.name as customer_name, u.full_name as user_name
            FROM customer_payments p
            LEFT JOIN customers c ON p.customer_id = c.id
            LEFT JOIN users u ON p.user_id = u.id
            ORDER BY p.created_at DESC
            LIMIT $limit OFFSET $offset
        ");
        
        $total = fetchOne("SELECT COUNT(*) as total FROM customer_payments")['total'];
        
        echo json_encode([
            'payments' => $payments,
            'total' => $total,
            'page' => $page,
            'pages' => ceil($total / $limit)
        ]);
    }
}

function handlePost() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (empty($input['customer_id']) || empty($input['amount'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف العميل والمبلغ مطلوبان']);
        return;
    }
    
    $customerId = (int)$input['customer_id'];
    $amount = (float)$input['amount'];
    
    if ($amount <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'المبلغ يجب أن يكون أكبر من صفر']);
        return;
    }
    
    // التحقق من وجود العميل
    $customer = fetchOne("SELECT * FROM customers WHERE id = ? AND is_active = 1", [$customerId]);
    if (!$customer) {
        http_response_code(404);
        echo json_encode(['error' => 'العميل غير موجود']);
        return;
    }
    
    // التحقق من أن المبلغ لا يتجاوز الدين
    if ($amount > $customer['total_debt']) {
        http_response_code(400);
        echo json_encode(['error' => 'المبلغ المدفوع أكبر من إجمالي الدين']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // إدراج الدفعة
        $sql = "INSERT INTO customer_payments (customer_id, amount, payment_method, notes, user_id) 
                VALUES (?, ?, ?, ?, ?)";
        
        $params = [
            $customerId,
            $amount,
            sanitize($input['payment_method'] ?? 'cash'),
            sanitize($input['notes'] ?? ''),
            $_SESSION['user_id']
        ];
        
        if (!executeQuery($sql, $params)) {
            throw new Exception('خطأ في حفظ الدفعة');
        }
        
        // تحديث رصيد العميل
        $updateSql = "UPDATE customers SET total_debt = total_debt - ? WHERE id = ?";
        if (!executeQuery($updateSql, [$amount, $customerId])) {
            throw new Exception('خطأ في تحديث رصيد العميل');
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ الدفعة بنجاح',
            'payment_id' => getLastInsertId()
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function handleDelete() {
    global $pdo;
    
    // التحقق من الصلاحيات
    if ($_SESSION['user_role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'غير مصرح لك بهذه العملية']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف الدفعة مطلوب']);
        return;
    }
    
    $paymentId = (int)$input['id'];
    
    // جلب تفاصيل الدفعة
    $payment = fetchOne("SELECT * FROM customer_payments WHERE id = ?", [$paymentId]);
    if (!$payment) {
        http_response_code(404);
        echo json_encode(['error' => 'الدفعة غير موجودة']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // إرجاع المبلغ لرصيد العميل
        $updateSql = "UPDATE customers SET total_debt = total_debt + ? WHERE id = ?";
        executeQuery($updateSql, [$payment['amount'], $payment['customer_id']]);
        
        // حذف الدفعة
        executeQuery("DELETE FROM customer_payments WHERE id = ?", [$paymentId]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الدفعة بنجاح'
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}
?>
