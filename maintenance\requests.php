<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];

// جلب طلبات الصيانة
try {
    $requests = fetchAll("
        SELECT * FROM maintenance_requests 
        ORDER BY created_at DESC
    ") ?: [];
} catch (Exception $e) {
    $requests = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبات الصيانة - نظام إدارة المحل</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <span>إدارة المحل</span>
                </div>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="../admin/dashboard.php" class="nav-link"><i class="fas fa-tachometer-alt"></i><span>لوحة التحكم</span></a></li>
                    <li><a href="../pos/index.php" class="nav-link"><i class="fas fa-cash-register"></i><span>نقطة البيع</span></a></li>
                    <li><a href="../products/index.php" class="nav-link"><i class="fas fa-boxes"></i><span>إدارة المنتجات</span></a></li>
                    <li><a href="../customers/index.php" class="nav-link"><i class="fas fa-users"></i><span>إدارة العملاء</span></a></li>
                    <li><a href="../suppliers/index.php" class="nav-link"><i class="fas fa-truck"></i><span>إدارة الموردين</span></a></li>
                    <li><a href="../maintenance/index.php" class="nav-link active"><i class="fas fa-tools"></i><span>نظام الصيانة</span></a></li>
                    <li><a href="../reports/index.php" class="nav-link"><i class="fas fa-chart-bar"></i><span>التقارير</span></a></li>
                    <?php if ($user_role === 'admin'): ?>
                    <li><a href="../settings/index.php" class="nav-link"><i class="fas fa-cog"></i><span>الإعدادات</span></a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar"><i class="fas fa-user"></i></div>
                    <div class="user-details">
                        <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                        <span class="user-role"><?php echo $user_role === 'admin' ? 'مدير' : 'موظف'; ?></span>
                    </div>
                </div>
                <a href="../auth/logout.php" class="logout-btn" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>طلبات الصيانة</h1>
                    <div class="breadcrumb">
                        <span>الرئيسية</span>
                        <i class="fas fa-chevron-left"></i>
                        <span>طلبات الصيانة</span>
                    </div>
                </div>
                
                <div class="top-bar-right">
                    <div class="datetime">
                        <div id="current-time"></div>
                        <div id="current-date"></div>
                    </div>
                </div>
            </header>

            <div class="page-content">
                <div class="content-header">
                    <div class="content-title">
                        <h2><i class="fas fa-bell"></i> طلبات الصيانة من العملاء</h2>
                        <p>إدارة ومتابعة طلبات الصيانة الواردة من العملاء</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>

                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>نوع الجهاز</th>
                                <th>المشكلة</th>
                                <th>الحالة</th>
                                <th>تاريخ الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($requests)): ?>
                            <tr>
                                <td colspan="8" class="text-center">لا توجد طلبات صيانة</td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($requests as $request): ?>
                            <tr>
                                <td>#<?php echo $request['id']; ?></td>
                                <td><?php echo htmlspecialchars($request['customer_name']); ?></td>
                                <td>
                                    <a href="tel:<?php echo $request['customer_phone']; ?>" class="phone-link">
                                        <?php echo htmlspecialchars($request['customer_phone']); ?>
                                    </a>
                                </td>
                                <td><?php echo htmlspecialchars($request['device_type']); ?></td>
                                <td class="description-cell" title="<?php echo htmlspecialchars($request['problem_description']); ?>">
                                    <?php echo mb_substr($request['problem_description'], 0, 50) . '...'; ?>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $request['status']; ?>">
                                        <?php 
                                        $statuses = [
                                            'pending' => 'في الانتظار',
                                            'contacted' => 'تم التواصل',
                                            'received' => 'تم الاستلام',
                                            'in_progress' => 'قيد الإصلاح',
                                            'ready' => 'جاهز',
                                            'delivered' => 'تم التسليم'
                                        ];
                                        echo $statuses[$request['status']] ?? $request['status'];
                                        ?>
                                    </span>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-small btn-primary" onclick="viewRequest(<?php echo $request['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="tel:<?php echo $request['customer_phone']; ?>" class="btn-small btn-success">
                                            <i class="fas fa-phone"></i>
                                        </a>
                                        <a href="https://wa.me/<?php echo str_replace('+', '', $request['customer_phone']); ?>" target="_blank" class="btn-small btn-info">
                                            <i class="fab fa-whatsapp"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        function viewRequest(id) {
            // يمكن إضافة نافذة منبثقة لعرض تفاصيل الطلب
            alert('عرض تفاصيل الطلب رقم: ' + id);
        }
    </script>
</body>
</html>
