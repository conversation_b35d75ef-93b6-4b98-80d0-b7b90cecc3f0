<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];

// جلب عمليات الإرجاع
try {
    $returns = fetchAll("
        SELECT r.*, p.name as product_name, c.name as customer_name
        FROM product_returns r
        LEFT JOIN products p ON r.product_id = p.id
        LEFT JOIN customers c ON r.customer_id = c.id
        ORDER BY r.created_at DESC
    ") ?: [];
} catch (Exception $e) {
    $returns = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إرجاع المنتجات - نظام إدارة المحل</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <span>إدارة المحل</span>
                </div>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="../admin/dashboard.php" class="nav-link"><i class="fas fa-tachometer-alt"></i><span>لوحة التحكم</span></a></li>
                    <li><a href="../pos/index.php" class="nav-link"><i class="fas fa-cash-register"></i><span>نقطة البيع</span></a></li>
                    <li><a href="../products/index.php" class="nav-link"><i class="fas fa-boxes"></i><span>إدارة المنتجات</span></a></li>
                    <li><a href="../customers/index.php" class="nav-link"><i class="fas fa-users"></i><span>إدارة العملاء</span></a></li>
                    <li><a href="../suppliers/index.php" class="nav-link"><i class="fas fa-truck"></i><span>إدارة الموردين</span></a></li>
                    <li><a href="../maintenance/index.php" class="nav-link"><i class="fas fa-tools"></i><span>نظام الصيانة</span></a></li>
                    <li><a href="index.php" class="nav-link active"><i class="fas fa-undo"></i><span>الإرجاع والاستبدال</span></a></li>
                    <li><a href="../reports/index.php" class="nav-link"><i class="fas fa-chart-bar"></i><span>التقارير</span></a></li>
                    <?php if ($user_role === 'admin'): ?>
                    <li><a href="../settings/index.php" class="nav-link"><i class="fas fa-cog"></i><span>الإعدادات</span></a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar"><i class="fas fa-user"></i></div>
                    <div class="user-details">
                        <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                        <span class="user-role"><?php echo $user_role === 'admin' ? 'مدير' : 'موظف'; ?></span>
                    </div>
                </div>
                <a href="../auth/logout.php" class="logout-btn" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>إرجاع واستبدال المنتجات</h1>
                    <div class="breadcrumb">
                        <span>الرئيسية</span>
                        <i class="fas fa-chevron-left"></i>
                        <span>الإرجاع والاستبدال</span>
                    </div>
                </div>
                
                <div class="top-bar-right">
                    <button class="btn btn-primary" onclick="showReturnModal()">
                        <i class="fas fa-plus"></i> إرجاع منتج
                    </button>
                    <button class="btn btn-secondary" onclick="showExchangeModal()">
                        <i class="fas fa-exchange-alt"></i> استبدال منتج
                    </button>
                </div>
            </header>

            <div class="page-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-undo"></i>
                        </div>
                        <div class="stat-info">
                            <h3>إجمالي الإرجاع</h3>
                            <p class="stat-number"><?php echo count(array_filter($returns, fn($r) => $r['type'] === 'return')); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3>إجمالي الاستبدال</h3>
                            <p class="stat-number"><?php echo count(array_filter($returns, fn($r) => $r['type'] === 'exchange')); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3>في الانتظار</h3>
                            <p class="stat-number"><?php echo count(array_filter($returns, fn($r) => $r['status'] === 'pending')); ?></p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stat-info">
                            <h3>مكتملة</h3>
                            <p class="stat-number"><?php echo count(array_filter($returns, fn($r) => $r['status'] === 'completed')); ?></p>
                        </div>
                    </div>
                </div>

                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>رقم العملية</th>
                                <th>النوع</th>
                                <th>المنتج</th>
                                <th>العميل</th>
                                <th>السبب</th>
                                <th>الكمية</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($returns)): ?>
                            <tr>
                                <td colspan="10" class="text-center">لا توجد عمليات إرجاع أو استبدال</td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($returns as $return): ?>
                            <tr>
                                <td>#<?php echo $return['id']; ?></td>
                                <td>
                                    <span class="type-badge type-<?php echo $return['type']; ?>">
                                        <?php echo $return['type'] === 'return' ? 'إرجاع' : 'استبدال'; ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($return['product_name'] ?? 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($return['customer_name'] ?? 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($return['reason']); ?></td>
                                <td><?php echo $return['quantity']; ?></td>
                                <td><?php echo formatMoney($return['amount']); ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $return['status']; ?>">
                                        <?php 
                                        $statuses = [
                                            'pending' => 'في الانتظار',
                                            'approved' => 'موافق عليه',
                                            'completed' => 'مكتمل',
                                            'rejected' => 'مرفوض'
                                        ];
                                        echo $statuses[$return['status']] ?? $return['status'];
                                        ?>
                                    </span>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($return['created_at'])); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-small btn-primary" onclick="viewReturn(<?php echo $return['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($return['status'] === 'pending'): ?>
                                        <button class="btn-small btn-success" onclick="approveReturn(<?php echo $return['id']; ?>)">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn-small btn-danger" onclick="rejectReturn(<?php echo $return['id']; ?>)">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        function showReturnModal() {
            alert('نافذة إرجاع المنتج - قيد التطوير');
        }
        
        function showExchangeModal() {
            alert('نافذة استبدال المنتج - قيد التطوير');
        }
        
        function viewReturn(id) {
            alert('عرض تفاصيل العملية رقم: ' + id);
        }
        
        function approveReturn(id) {
            if (confirm('هل أنت متأكد من الموافقة على هذه العملية؟')) {
                // إرسال طلب الموافقة
                alert('تم الموافقة على العملية');
            }
        }
        
        function rejectReturn(id) {
            if (confirm('هل أنت متأكد من رفض هذه العملية؟')) {
                // إرسال طلب الرفض
                alert('تم رفض العملية');
            }
        }
    </script>
</body>
</html>
