<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقطة البيع - نظام إدارة المحل</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/pos.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="pos-container">
        <!-- Header -->
        <header class="pos-header">
            <div class="header-content">
                <div class="header-left">
                    <h1><i class="fas fa-cash-register"></i> نقطة البيع</h1>
                    <span class="cashier">الكاشير: <?php echo htmlspecialchars($user_name); ?></span>
                </div>
                <div class="header-right">
                    <div class="datetime">
                        <div id="current-time"></div>
                        <div id="current-date"></div>
                    </div>
                    <a href="../index.php" class="back-btn">
                        <i class="fas fa-arrow-right"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </header>

        <div class="pos-layout">
            <!-- قسم البحث والمنتجات -->
            <div class="products-section">
                <!-- شريط البحث -->
                <div class="search-section">
                    <div class="search-box">
                        <input type="text" id="product-search" placeholder="البحث بالاسم أو الباركود...">
                        <button id="barcode-scan" class="scan-btn">
                            <i class="fas fa-qrcode"></i>
                        </button>
                    </div>
                    <div class="category-filter">
                        <button class="filter-btn active" data-category="all">الكل</button>
                        <button class="filter-btn" data-category="mobile">موبايل</button>
                        <button class="filter-btn" data-category="accessories">اكسسوارات</button>
                        <button class="filter-btn" data-category="parts">قطع غيار</button>
                    </div>
                </div>

                <!-- قائمة المنتجات -->
                <div class="products-grid" id="products-grid">
                    <!-- سيتم تحميل المنتجات هنا عبر JavaScript -->
                </div>
            </div>

            <!-- قسم الفاتورة -->
            <div class="invoice-section">
                <div class="invoice-header">
                    <h2><i class="fas fa-receipt"></i> الفاتورة</h2>
                    <div class="invoice-number">
                        رقم الفاتورة: <span id="invoice-number"><?php echo date('Ymd') . rand(1000, 9999); ?></span>
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="customer-section">
                    <div class="customer-select">
                        <select id="customer-select">
                            <option value="">عميل نقدي</option>
                        </select>
                        <button id="add-customer" class="add-btn">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>

                <!-- عناصر الفاتورة -->
                <div class="invoice-items">
                    <div class="items-header">
                        <span>المنتج</span>
                        <span>الكمية</span>
                        <span>السعر</span>
                        <span>المجموع</span>
                        <span>حذف</span>
                    </div>
                    <div class="items-list" id="invoice-items">
                        <!-- سيتم إضافة العناصر هنا -->
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="invoice-summary">
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotal">0 د.ع</span>
                    </div>
                    <div class="summary-row discount-row">
                        <span>الخصم:</span>
                        <div class="discount-input">
                            <input type="number" id="discount" value="0" min="0">
                            <select id="discount-type">
                                <option value="amount">د.ع</option>
                                <option value="percent">%</option>
                            </select>
                        </div>
                    </div>
                    <div class="summary-row total-row">
                        <span>المجموع الكلي:</span>
                        <span id="total">0 د.ع</span>
                    </div>
                </div>

                <!-- قسم الدفع -->
                <div class="payment-section">
                    <div class="payment-type">
                        <label>
                            <input type="radio" name="payment-type" value="cash" checked>
                            <span>نقدي</span>
                        </label>
                        <label>
                            <input type="radio" name="payment-type" value="credit">
                            <span>آجل</span>
                        </label>
                    </div>
                    
                    <div class="payment-amount" id="cash-payment">
                        <div class="amount-row">
                            <span>المبلغ المدفوع:</span>
                            <input type="number" id="paid-amount" value="0" min="0">
                        </div>
                        <div class="amount-row">
                            <span>الباقي:</span>
                            <span id="change">0 د.ع</span>
                        </div>
                    </div>
                </div>

                <!-- أزرار العمليات -->
                <div class="action-buttons">
                    <button id="clear-invoice" class="btn btn-secondary">
                        <i class="fas fa-trash"></i> مسح الفاتورة
                    </button>
                    <button id="save-invoice" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الفاتورة
                    </button>
                    <button id="print-invoice" class="btn btn-success">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة عميل جديد -->
    <div id="customer-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة عميل جديد</h3>
                <button class="close-modal">&times;</button>
            </div>
            <form id="customer-form">
                <div class="form-group">
                    <label>اسم العميل:</label>
                    <input type="text" name="name" required>
                </div>
                <div class="form-group">
                    <label>رقم الهاتف:</label>
                    <input type="tel" name="phone">
                </div>
                <div class="form-group">
                    <label>العنوان:</label>
                    <textarea name="address"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary close-modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/pos.js"></script>
    <script>
        // تهيئة نقطة البيع
        document.addEventListener('DOMContentLoaded', function() {
            POS.init();
        });
    </script>
</body>
</html>
