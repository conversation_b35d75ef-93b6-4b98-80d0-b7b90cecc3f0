// لوحة التحكم الإدارية
const AdminDashboard = {
    charts: {},
    refreshInterval: null,

    // تهيئة لوحة التحكم
    init() {
        this.bindEvents();
        this.loadDashboardData();
        this.initCharts();
        this.startAutoRefresh();
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);
    },

    // ربط الأحداث
    bindEvents() {
        // تبديل الشريط الجانبي
        document.getElementById('sidebar-toggle').addEventListener('click', () => {
            this.toggleSidebar();
        });

        // تحديث البيانات
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.loadDashboardData();
            }
        });

        // النقر على بطاقات الإحصائيات
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('click', () => {
                this.handleStatCardClick(card);
            });
        });
    },

    // تبديل الشريط الجانبي
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('collapsed');
        
        // حفظ الحالة
        localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
    },

    // تحميل بيانات لوحة التحكم
    async loadDashboardData() {
        try {
            await Promise.all([
                this.loadStats(),
                this.loadRecentOrders(),
                this.loadLowStockProducts(),
                this.loadMaintenanceStatus()
            ]);
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            showMessage('خطأ في تحميل بيانات لوحة التحكم', 'error');
        }
    },

    // تحميل الإحصائيات
    async loadStats() {
        try {
            const response = await fetch('../api/dashboard_stats.php');
            const data = await response.json();
            
            if (response.ok) {
                document.getElementById('today-sales').textContent = data.today_sales + ' د.ع';
                document.getElementById('today-invoices').textContent = data.today_invoices;
                document.getElementById('maintenance-count').textContent = data.maintenance_count;
                document.getElementById('low-stock').textContent = data.low_stock;
                
                // تحديث الرسوم البيانية
                this.updateSalesChart(data.weekly_sales || []);
            }
        } catch (error) {
            console.error('Error loading stats:', error);
        }
    },

    // تحميل آخر الفواتير
    async loadRecentOrders() {
        try {
            const response = await fetch('../api/recent_orders.php');
            const orders = await response.json();
            
            if (response.ok) {
                const container = document.getElementById('recent-orders');
                
                if (orders.length === 0) {
                    container.innerHTML = '<p style="text-align: center; color: #6c757d;">لا توجد فواتير حديثة</p>';
                    return;
                }
                
                container.innerHTML = orders.map(order => `
                    <div class="order-item">
                        <div class="order-info">
                            <h4>فاتورة #${order.invoice_number}</h4>
                            <p>${order.customer_name || 'عميل نقدي'} - ${this.formatDateTime(order.created_at)}</p>
                        </div>
                        <div class="order-amount">${formatMoney(order.total)}</div>
                    </div>
                `).join('');
            }
        } catch (error) {
            console.error('Error loading recent orders:', error);
        }
    },

    // تحميل المنتجات منخفضة المخزون
    async loadLowStockProducts() {
        try {
            const response = await fetch('../api/low_stock_products.php');
            const products = await response.json();
            
            if (response.ok) {
                const container = document.getElementById('low-stock-products');
                
                if (products.length === 0) {
                    container.innerHTML = '<p style="text-align: center; color: #28a745;">جميع المنتجات متوفرة</p>';
                    return;
                }
                
                container.innerHTML = products.map(product => `
                    <div class="stock-item">
                        <h4>${product.name}</h4>
                        <span class="stock-quantity">${product.quantity} متبقي</span>
                    </div>
                `).join('');
            }
        } catch (error) {
            console.error('Error loading low stock products:', error);
        }
    },

    // تحميل حالة الصيانة
    async loadMaintenanceStatus() {
        try {
            const response = await fetch('../api/maintenance_status.php');
            const maintenance = await response.json();
            
            if (response.ok) {
                const container = document.getElementById('maintenance-status');
                
                if (maintenance.length === 0) {
                    container.innerHTML = '<p style="text-align: center; color: #6c757d;">لا توجد أجهزة قيد الصيانة</p>';
                    return;
                }
                
                container.innerHTML = maintenance.map(item => `
                    <div class="maintenance-item">
                        <h4>${item.device_type} - ${item.customer_name}</h4>
                        <span class="maintenance-status-badge status-${item.status}">
                            ${this.getMaintenanceStatusText(item.status)}
                        </span>
                    </div>
                `).join('');
            }
        } catch (error) {
            console.error('Error loading maintenance status:', error);
        }
    },

    // تهيئة الرسوم البيانية
    initCharts() {
        this.initSalesChart();
    },

    // تهيئة رسم المبيعات
    initSalesChart() {
        const ctx = document.getElementById('salesChart');
        if (!ctx) return;

        this.charts.sales = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                datasets: [{
                    label: 'المبيعات (د.ع)',
                    data: [0, 0, 0, 0, 0, 0, 0],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#2563eb',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return formatNumber(value) + ' د.ع';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                }
            }
        });
    },

    // تحديث رسم المبيعات
    updateSalesChart(data) {
        if (this.charts.sales && data.length > 0) {
            this.charts.sales.data.datasets[0].data = data;
            this.charts.sales.update();
        }
    },

    // بدء التحديث التلقائي
    startAutoRefresh() {
        // تحديث كل 5 دقائق
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData();
        }, 5 * 60 * 1000);
    },

    // إيقاف التحديث التلقائي
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    },

    // تحديث التاريخ والوقت
    updateDateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-EG', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        const dateString = now.toLocaleDateString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        
        const timeElement = document.getElementById('current-time');
        const dateElement = document.getElementById('current-date');
        
        if (timeElement) timeElement.textContent = timeString;
        if (dateElement) dateElement.textContent = dateString;
    },

    // معالجة النقر على بطاقات الإحصائيات
    handleStatCardClick(card) {
        if (card.classList.contains('sales')) {
            window.location.href = '../reports/index.php?tab=sales';
        } else if (card.classList.contains('orders')) {
            window.location.href = '../pos/index.php';
        } else if (card.classList.contains('maintenance')) {
            window.location.href = '../maintenance/index.php';
        } else if (card.classList.contains('inventory')) {
            window.location.href = '../products/index.php?filter=low_stock';
        }
    },

    // تنسيق التاريخ والوقت
    formatDateTime(datetime) {
        return new Date(datetime).toLocaleString('ar-EG', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    // الحصول على نص حالة الصيانة
    getMaintenanceStatusText(status) {
        const statuses = {
            'received': 'مستلم',
            'in_progress': 'قيد التصليح',
            'ready': 'جاهز للتسليم',
            'delivered': 'مسلم'
        };
        return statuses[status] || status;
    },

    // تصدير البيانات
    async exportData(type) {
        try {
            const response = await fetch(`../api/export.php?type=${type}`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${type}_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showMessage('تم تصدير البيانات بنجاح', 'success');
            } else {
                throw new Error('خطأ في تصدير البيانات');
            }
        } catch (error) {
            console.error('Error exporting data:', error);
            showMessage('خطأ في تصدير البيانات', 'error');
        }
    },

    // إنشاء نسخة احتياطية
    async createBackup() {
        try {
            const response = await fetch('../api/backup.php', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            } else {
                throw new Error(result.error || 'خطأ في إنشاء النسخة الاحتياطية');
            }
        } catch (error) {
            console.error('Error creating backup:', error);
            showMessage('خطأ في إنشاء النسخة الاحتياطية', 'error');
        }
    }
};

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // استعادة حالة الشريط الجانبي
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed) {
        document.getElementById('sidebar').classList.add('collapsed');
    }
    
    // إضافة تأثيرات الحركة
    const cards = document.querySelectorAll('.stat-card, .dashboard-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
});

// إضافة CSS للحركات
const style = document.createElement('style');
style.textContent = `
    .fade-in {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
        transform: translateY(20px);
    }
    
    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .stat-card {
        cursor: pointer;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }
`;
document.head.appendChild(style);
