<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost();
            break;
        case 'PUT':
            handlePut();
            break;
        case 'DELETE':
            handleDelete();
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'طريقة غير مدعومة']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

function handleGet() {
    global $pdo;
    
    if (isset($_GET['id'])) {
        // جلب عميل واحد
        $id = (int)$_GET['id'];
        $customer = fetchOne("SELECT * FROM customers WHERE id = ? AND is_active = 1", [$id]);
        
        if ($customer) {
            echo json_encode($customer);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'العميل غير موجود']);
        }
    } elseif (isset($_GET['statement'])) {
        // كشف حساب العميل
        $customerId = (int)$_GET['statement'];
        getCustomerStatement($customerId);
    } else {
        // جلب جميع العملاء
        $search = $_GET['search'] ?? '';
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 50);
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT * FROM customers WHERE is_active = 1";
        $params = [];
        
        if (!empty($search)) {
            $sql .= " AND (name LIKE ? OR phone LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $sql .= " ORDER BY name LIMIT $limit OFFSET $offset";
        
        $customers = fetchAll($sql, $params);
        
        // حساب العدد الكلي
        $countSql = "SELECT COUNT(*) as total FROM customers WHERE is_active = 1";
        $countParams = [];
        
        if (!empty($search)) {
            $countSql .= " AND (name LIKE ? OR phone LIKE ?)";
            $countParams[] = "%$search%";
            $countParams[] = "%$search%";
        }
        
        $total = fetchOne($countSql, $countParams)['total'];
        
        echo json_encode([
            'customers' => $customers,
            'total' => $total,
            'page' => $page,
            'pages' => ceil($total / $limit)
        ]);
    }
}

function handlePost() {
    global $pdo;
    
    // قراءة البيانات من النموذج أو JSON
    if ($_SERVER['CONTENT_TYPE'] === 'application/json') {
        $input = json_decode(file_get_contents('php://input'), true);
    } else {
        $input = $_POST;
    }
    
    // التحقق من البيانات المطلوبة
    if (empty($input['name'])) {
        http_response_code(400);
        echo json_encode(['error' => 'اسم العميل مطلوب']);
        return;
    }
    
    // التحقق من عدم تكرار رقم الهاتف
    if (!empty($input['phone'])) {
        $existing = fetchOne("SELECT id FROM customers WHERE phone = ? AND is_active = 1", [$input['phone']]);
        if ($existing) {
            http_response_code(400);
            echo json_encode(['error' => 'رقم الهاتف موجود مسبقاً']);
            return;
        }
    }
    
    $sql = "INSERT INTO customers (name, phone, address, email, notes) VALUES (?, ?, ?, ?, ?)";
    
    $params = [
        sanitize($input['name']),
        sanitize($input['phone'] ?? ''),
        sanitize($input['address'] ?? ''),
        sanitize($input['email'] ?? ''),
        sanitize($input['notes'] ?? '')
    ];
    
    if (executeQuery($sql, $params)) {
        $customerId = getLastInsertId();
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة العميل بنجاح',
            'customer_id' => $customerId
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في إضافة العميل']);
    }
}

function handlePut() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف العميل مطلوب']);
        return;
    }
    
    $id = (int)$input['id'];
    
    // التحقق من وجود العميل
    $customer = fetchOne("SELECT * FROM customers WHERE id = ? AND is_active = 1", [$id]);
    if (!$customer) {
        http_response_code(404);
        echo json_encode(['error' => 'العميل غير موجود']);
        return;
    }
    
    // التحقق من عدم تكرار رقم الهاتف
    if (!empty($input['phone']) && $input['phone'] !== $customer['phone']) {
        $existing = fetchOne("SELECT id FROM customers WHERE phone = ? AND id != ? AND is_active = 1", [$input['phone'], $id]);
        if ($existing) {
            http_response_code(400);
            echo json_encode(['error' => 'رقم الهاتف موجود مسبقاً']);
            return;
        }
    }
    
    $sql = "UPDATE customers SET 
            name = ?, 
            phone = ?, 
            address = ?, 
            email = ?, 
            notes = ?,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";
    
    $params = [
        sanitize($input['name'] ?? $customer['name']),
        sanitize($input['phone'] ?? $customer['phone']),
        sanitize($input['address'] ?? $customer['address']),
        sanitize($input['email'] ?? $customer['email']),
        sanitize($input['notes'] ?? $customer['notes']),
        $id
    ];
    
    if (executeQuery($sql, $params)) {
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث العميل بنجاح'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في تحديث العميل']);
    }
}

function handleDelete() {
    global $pdo;
    
    // التحقق من الصلاحيات
    if ($_SESSION['user_role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'غير مصرح لك بهذه العملية']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'معرف العميل مطلوب']);
        return;
    }
    
    $id = (int)$input['id'];
    
    // التحقق من وجود العميل
    $customer = fetchOne("SELECT * FROM customers WHERE id = ? AND is_active = 1", [$id]);
    if (!$customer) {
        http_response_code(404);
        echo json_encode(['error' => 'العميل غير موجود']);
        return;
    }
    
    // التحقق من وجود معاملات للعميل
    $hasSales = fetchOne("SELECT COUNT(*) as count FROM sales WHERE customer_id = ?", [$id])['count'];
    if ($hasSales > 0) {
        http_response_code(400);
        echo json_encode(['error' => 'لا يمكن حذف العميل لوجود معاملات مرتبطة به']);
        return;
    }
    
    // حذف منطقي (تعطيل العميل)
    if (executeQuery("UPDATE customers SET is_active = 0 WHERE id = ?", [$id])) {
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف العميل بنجاح'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في حذف العميل']);
    }
}

function getCustomerStatement($customerId) {
    global $pdo;
    
    // التحقق من وجود العميل
    $customer = fetchOne("SELECT * FROM customers WHERE id = ? AND is_active = 1", [$customerId]);
    if (!$customer) {
        http_response_code(404);
        echo json_encode(['error' => 'العميل غير موجود']);
        return;
    }
    
    // جلب المبيعات
    $sales = fetchAll("
        SELECT s.*, 
               DATE_FORMAT(s.created_at, '%Y-%m-%d %H:%i') as sale_date
        FROM sales s 
        WHERE s.customer_id = ? 
        ORDER BY s.created_at DESC
    ", [$customerId]);
    
    // جلب المدفوعات
    $payments = fetchAll("
        SELECT p.*, 
               DATE_FORMAT(p.created_at, '%Y-%m-%d %H:%i') as payment_date
        FROM customer_payments p 
        WHERE p.customer_id = ? 
        ORDER BY p.created_at DESC
    ", [$customerId]);
    
    // حساب الإجماليات
    $totalSales = fetchOne("SELECT COALESCE(SUM(total), 0) as total FROM sales WHERE customer_id = ?", [$customerId])['total'];
    $totalPayments = fetchOne("SELECT COALESCE(SUM(amount), 0) as total FROM customer_payments WHERE customer_id = ?", [$customerId])['total'];
    $balance = $totalSales - $totalPayments;
    
    echo json_encode([
        'customer' => $customer,
        'sales' => $sales,
        'payments' => $payments,
        'summary' => [
            'total_sales' => $totalSales,
            'total_payments' => $totalPayments,
            'balance' => $balance
        ]
    ]);
}
?>
