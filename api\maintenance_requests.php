<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

try {
    // جلب طلبات الصيانة الجديدة
    $requests = fetchAll("
        SELECT * FROM maintenance_requests 
        WHERE status = 'pending' 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    
    echo json_encode($requests ?: []);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في جلب طلبات الصيانة: ' . $e->getMessage()]);
}
?>
