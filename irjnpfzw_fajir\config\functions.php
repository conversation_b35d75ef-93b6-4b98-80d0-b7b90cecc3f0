<?php
// وظائف مساعدة إضافية

/**
 * تنسيق الأرقام للعرض
 */
function formatNumber($number, $decimals = 0) {
    return number_format($number, $decimals, '.', ',');
}

/**
 * تنسيق المبالغ المالية
 */
function formatMoney($amount, $currency = 'د.ع') {
    return formatNumber($amount, 0) . ' ' . $currency;
}

/**
 * تنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) return '-';
    return date($format, strtotime($date));
}

/**
 * تنسيق التاريخ والوقت
 */
function formatDateTime($datetime, $format = 'Y-m-d H:i') {
    if (empty($datetime)) return '-';
    return date($format, strtotime($datetime));
}

/**
 * إنشاء رقم فاتورة فريد
 */
function generateInvoiceNumber() {
    $prefix = 'INV';
    $date = date('Ymd');
    $random = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    return $prefix . $date . $random;
}

/**
 * إنشاء باركود فريد
 */
function generateBarcode() {
    return date('Ymd') . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * تحويل النص إلى slug
 */
function createSlug($text) {
    $text = trim($text);
    $text = preg_replace('/[^a-zA-Z0-9\s\u0600-\u06FF]/', '', $text);
    $text = preg_replace('/\s+/', '-', $text);
    return strtolower($text);
}

/**
 * التحقق من صحة رقم الهاتف العراقي
 */
function validateIraqiPhone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // التحقق من الطول والبداية
    if (strlen($phone) == 11 && substr($phone, 0, 3) == '964') {
        return true; // رقم دولي
    } elseif (strlen($phone) == 11 && substr($phone, 0, 2) == '07') {
        return true; // رقم محلي
    }
    
    return false;
}

/**
 * تنسيق رقم الهاتف
 */
function formatPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) == 11 && substr($phone, 0, 2) == '07') {
        return substr($phone, 0, 4) . '-' . substr($phone, 4, 3) . '-' . substr($phone, 7);
    }
    
    return $phone;
}

/**
 * حساب النسبة المئوية
 */
function calculatePercentage($part, $total) {
    if ($total == 0) return 0;
    return round(($part / $total) * 100, 2);
}

/**
 * حساب الخصم
 */
function calculateDiscount($original, $discounted) {
    if ($original == 0) return 0;
    return round((($original - $discounted) / $original) * 100, 2);
}

/**
 * تحويل الأرقام إلى كلمات (عربي)
 */
function numberToWords($number) {
    $ones = [
        '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
        'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر',
        'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
    ];
    
    $tens = [
        '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
    ];
    
    $hundreds = [
        '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
    ];
    
    if ($number == 0) return 'صفر';
    if ($number < 20) return $ones[$number];
    if ($number < 100) return $tens[intval($number / 10)] . ($number % 10 ? ' ' . $ones[$number % 10] : '');
    if ($number < 1000) return $hundreds[intval($number / 100)] . ($number % 100 ? ' ' . numberToWords($number % 100) : '');
    
    return $number; // للأرقام الكبيرة، إرجاع الرقم كما هو
}

/**
 * ضغط الصور
 */
function compressImage($source, $destination, $quality = 80) {
    $info = getimagesize($source);
    
    if ($info['mime'] == 'image/jpeg') {
        $image = imagecreatefromjpeg($source);
    } elseif ($info['mime'] == 'image/gif') {
        $image = imagecreatefromgif($source);
    } elseif ($info['mime'] == 'image/png') {
        $image = imagecreatefrompng($source);
    } else {
        return false;
    }
    
    return imagejpeg($image, $destination, $quality);
}

/**
 * تغيير حجم الصورة
 */
function resizeImage($source, $destination, $width, $height) {
    list($originalWidth, $originalHeight) = getimagesize($source);
    
    $ratio = min($width / $originalWidth, $height / $originalHeight);
    $newWidth = $originalWidth * $ratio;
    $newHeight = $originalHeight * $ratio;
    
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    $sourceImage = imagecreatefromjpeg($source);
    
    imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
    
    return imagejpeg($newImage, $destination, 90);
}

/**
 * إنشاء مجلد إذا لم يكن موجوداً
 */
function createDirectoryIfNotExists($path) {
    if (!is_dir($path)) {
        return mkdir($path, 0755, true);
    }
    return true;
}

/**
 * حذف ملف بأمان
 */
function safeDeleteFile($filePath) {
    if (file_exists($filePath) && is_file($filePath)) {
        return unlink($filePath);
    }
    return false;
}

/**
 * تسجيل الأنشطة
 */
function logActivity($action, $details = '', $userId = null) {
    global $pdo;
    
    if (!$userId && isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
    }
    
    try {
        $sql = "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())";
        
        $params = [
            $userId,
            $action,
            $details,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        executeQuery($sql, $params);
    } catch (Exception $e) {
        // تجاهل أخطاء تسجيل الأنشطة
        error_log('Activity log error: ' . $e->getMessage());
    }
}

/**
 * إنشاء نسخة احتياطية من قاعدة البيانات
 */
function createDatabaseBackup($filename = null) {
    if (!$filename) {
        $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    }
    
    $backupDir = '../backups/';
    createDirectoryIfNotExists($backupDir);
    
    $command = sprintf(
        'mysqldump --user=%s --password=%s --host=%s %s > %s',
        DB_USER,
        DB_PASS,
        DB_HOST,
        DB_NAME,
        $backupDir . $filename
    );
    
    exec($command, $output, $return);
    
    return $return === 0 ? $backupDir . $filename : false;
}

/**
 * إرسال إشعار واتساب (يتطلب API خارجي)
 */
function sendWhatsAppNotification($phone, $message) {
    // هذه الوظيفة تحتاج إلى تكامل مع API واتساب
    // يمكن استخدام خدمات مثل Twilio أو WhatsApp Business API
    
    // مثال على التنفيذ:
    /*
    $apiUrl = 'https://api.whatsapp.com/send';
    $data = [
        'phone' => $phone,
        'text' => $message
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
    */
    
    return false; // مؤقتاً
}

/**
 * التحقق من انتهاء صلاحية الجلسة
 */
function checkSessionExpiry() {
    if (isset($_SESSION['last_activity'])) {
        $sessionTimeout = 3600; // ساعة واحدة
        if (time() - $_SESSION['last_activity'] > $sessionTimeout) {
            session_destroy();
            return false;
        }
    }
    $_SESSION['last_activity'] = time();
    return true;
}

/**
 * تشفير البيانات الحساسة
 */
function encryptData($data, $key = null) {
    if (!$key) {
        $key = 'irjnpfzw_fajir_secret_key'; // يجب تغييرها في الإنتاج
    }
    
    $cipher = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($cipher));
    $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);
    
    return base64_encode($iv . $encrypted);
}

/**
 * فك تشفير البيانات
 */
function decryptData($encryptedData, $key = null) {
    if (!$key) {
        $key = 'irjnpfzw_fajir_secret_key'; // يجب أن تكون نفس المفتاح
    }
    
    $cipher = 'AES-256-CBC';
    $data = base64_decode($encryptedData);
    $ivLength = openssl_cipher_iv_length($cipher);
    $iv = substr($data, 0, $ivLength);
    $encrypted = substr($data, $ivLength);
    
    return openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
}
?>
