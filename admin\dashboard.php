<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المحل</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <span>إدارة المحل</span>
                </div>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php" class="nav-link active">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li>
                        <a href="../pos/index.php" class="nav-link">
                            <i class="fas fa-cash-register"></i>
                            <span>نقطة البيع</span>
                        </a>
                    </li>
                    <li>
                        <a href="../products/index.php" class="nav-link">
                            <i class="fas fa-boxes"></i>
                            <span>إدارة المنتجات</span>
                        </a>
                    </li>
                    <li>
                        <a href="../customers/index.php" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>إدارة العملاء</span>
                        </a>
                    </li>
                    <li>
                        <a href="../suppliers/index.php" class="nav-link">
                            <i class="fas fa-truck"></i>
                            <span>إدارة الموردين</span>
                        </a>
                    </li>
                    <li>
                        <a href="../maintenance/index.php" class="nav-link">
                            <i class="fas fa-tools"></i>
                            <span>نظام الصيانة</span>
                        </a>
                    </li>
                    <li>
                        <a href="../reports/index.php" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <?php if ($user_role === 'admin'): ?>
                    <li>
                        <a href="../settings/index.php" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                        <span class="user-role"><?php echo $user_role === 'admin' ? 'مدير' : 'موظف'; ?></span>
                    </div>
                </div>
                <a href="../auth/logout.php" class="logout-btn" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>لوحة التحكم</h1>
                    <div class="breadcrumb">
                        <span>الرئيسية</span>
                        <i class="fas fa-chevron-left"></i>
                        <span>لوحة التحكم</span>
                    </div>
                </div>

                <div class="top-bar-right">
                    <div class="datetime">
                        <div id="current-time"></div>
                        <div id="current-date"></div>
                    </div>
                    <a href="../index.php" class="view-site-btn" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        عرض الموقع
                    </a>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card sales">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h3>مبيعات اليوم</h3>
                            <p class="stat-value" id="today-sales">0 د.ع</p>
                            <span class="stat-change positive">+12% من أمس</span>
                        </div>
                    </div>

                    <div class="stat-card orders">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <h3>عدد الفواتير</h3>
                            <p class="stat-value" id="today-invoices">0</p>
                            <span class="stat-change positive">+5 فواتير جديدة</span>
                        </div>
                    </div>

                    <div class="stat-card maintenance">
                        <div class="stat-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="stat-info">
                            <h3>أجهزة قيد الصيانة</h3>
                            <p class="stat-value" id="maintenance-count">0</p>
                            <span class="stat-change neutral">3 جاهزة للتسليم</span>
                        </div>
                    </div>

                    <div class="stat-card inventory">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>تنبيهات المخزون</h3>
                            <p class="stat-value" id="low-stock">0</p>
                            <span class="stat-change negative">منتجات منخفضة</span>
                        </div>
                    </div>
                </div>

                <!-- Charts and Recent Activity -->
                <div class="dashboard-grid">
                    <!-- Sales Chart -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-line"></i> مبيعات الأسبوع</h3>
                            <div class="card-actions">
                                <button class="btn-small">عرض التفاصيل</button>
                            </div>
                        </div>
                        <div class="card-content">
                            <canvas id="salesChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <!-- Recent Orders -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-receipt"></i> آخر الفواتير</h3>
                            <div class="card-actions">
                                <a href="../pos/index.php" class="btn-small">فاتورة جديدة</a>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="recent-orders" id="recent-orders">
                                <!-- سيتم تحميل الفواتير هنا -->
                            </div>
                        </div>
                    </div>

                    <!-- Low Stock Products -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-boxes"></i> منتجات منخفضة المخزون</h3>
                            <div class="card-actions">
                                <a href="../products/index.php" class="btn-small">إدارة المنتجات</a>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="low-stock-products" id="low-stock-products">
                                <!-- سيتم تحميل المنتجات هنا -->
                            </div>
                        </div>
                    </div>

                    <!-- Maintenance Status -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-tools"></i> حالة الصيانة</h3>
                            <div class="card-actions">
                                <a href="../maintenance/index.php" class="btn-small">إدارة الصيانة</a>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="maintenance-status" id="maintenance-status">
                                <!-- سيتم تحميل حالة الصيانة هنا -->
                            </div>
                        </div>
                    </div>

                    <!-- طلبات الصيانة الجديدة -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-bell"></i> طلبات صيانة جديدة</h3>
                            <div class="card-actions">
                                <a href="../maintenance/requests.php" class="btn-small">عرض الكل</a>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="maintenance-requests" id="maintenance-requests">
                                <!-- سيتم تحميل طلبات الصيانة هنا -->
                            </div>
                        </div>
                    </div>

                    <!-- طلبات العملاء -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-shopping-cart"></i> طلبات العملاء</h3>
                            <div class="card-actions">
                                <a href="../orders/index.php" class="btn-small">إدارة الطلبات</a>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="customer-orders" id="customer-orders">
                                <!-- سيتم تحميل طلبات العملاء هنا -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
                    <div class="actions-grid">
                        <a href="../pos/index.php" class="action-btn">
                            <i class="fas fa-cash-register"></i>
                            <span>فاتورة جديدة</span>
                        </a>
                        <a href="../products/index.php" class="action-btn">
                            <i class="fas fa-plus"></i>
                            <span>إضافة منتج</span>
                        </a>
                        <a href="../customers/index.php" class="action-btn">
                            <i class="fas fa-user-plus"></i>
                            <span>عميل جديد</span>
                        </a>
                        <a href="../maintenance/index.php" class="action-btn">
                            <i class="fas fa-tools"></i>
                            <span>استقبال جهاز</span>
                        </a>
                        <a href="../reports/index.php" class="action-btn">
                            <i class="fas fa-file-alt"></i>
                            <span>تقرير مبيعات</span>
                        </a>
                        <a href="../settings/index.php" class="action-btn">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // تهيئة لوحة التحكم
        document.addEventListener('DOMContentLoaded', function() {
            AdminDashboard.init();
        });
    </script>
</body>
</html>
