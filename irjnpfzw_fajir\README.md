# نظام إدارة المحل المتكامل

نظام شامل لإدارة المحلات التجارية يشمل نقطة البيع، إدارة المنتجات، العملاء، الموردين، ونظام الصيانة.

## المميزات الرئيسية

### 🏪 نقطة البيع (POS)
- واجهة سهلة الاستخدام لنقطة البيع
- البحث عن المنتجات بالاسم أو الباركود
- دعم قارئ الباركود
- إضافة خصم للمنتج أو للفاتورة
- دعم البيع النقدي والآجل
- طباعة الفواتير

### 📦 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تصنيف المنتجات (موبايل، اكسسوارات، قطع غيار)
- إدارة المخزون والتنبيه عند النفاد
- استيراد وتصدير المنتجات من/إلى Excel
- إدارة الصور والباركود

### 👥 إدارة العملاء
- تسجيل بيانات العملاء
- متابعة الديون والمدفوعات
- كشف حساب العميل
- تاريخ المشتريات

### 🚚 إدارة الموردين
- تسجيل بيانات الموردين
- متابعة المشتريات والمدفوعات
- كشف حساب المورد

### 🔧 نظام الصيانة
- استقبال طلبات الصيانة
- متابعة حالة الأجهزة
- إدارة قطع الغيار المستخدمة
- طباعة إيصالات الاستلام

### 📊 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- تقارير الصيانة
- تقارير الأرباح والخسائر
- إحصائيات المخزون

## متطلبات التشغيل

- خادم ويب (Apache/Nginx)
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- متصفح ويب حديث

## التثبيت

### 1. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE irjnpfzw_fajir CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء المستخدم
CREATE USER 'irjnpfzw_fajir'@'localhost' IDENTIFIED BY 'irjnpfzw_fajir';
GRANT ALL PRIVILEGES ON irjnpfzw_fajir.* TO 'irjnpfzw_fajir'@'localhost';
FLUSH PRIVILEGES;
```

### 2. استيراد الجداول

```bash
mysql -u irjnpfzw_fajir -p irjnpfzw_fajir < database.sql
```

### 3. رفع الملفات

- ارفع مجلد `irjnpfzw_fajir` إلى خادم الويب
- تأكد من أن المجلد قابل للقراءة من قبل خادم الويب

### 4. إعداد الصلاحيات

```bash
chmod 755 irjnpfzw_fajir/
chmod 644 irjnpfzw_fajir/*.php
chmod 755 irjnpfzw_fajir/assets/
chmod 755 irjnpfzw_fajir/uploads/ # إنشاء مجلد للصور
```

## بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** password

⚠️ **مهم:** يرجى تغيير كلمة المرور فور تسجيل الدخول الأول

## الاستخدام

### تسجيل الدخول
1. افتح المتصفح وانتقل إلى: `http://your-domain.com/irjnpfzw_fajir/`
2. أدخل بيانات الدخول
3. ستظهر لك الواجهة الرئيسية

### إضافة منتج جديد
1. انتقل إلى "إدارة المنتجات"
2. اضغط على "إضافة منتج"
3. املأ البيانات المطلوبة
4. احفظ المنتج

### إجراء عملية بيع
1. انتقل إلى "نقطة البيع"
2. ابحث عن المنتجات وأضفها للسلة
3. اختر العميل (اختياري)
4. أضف خصم إذا لزم الأمر
5. اختر طريقة الدفع
6. احفظ واطبع الفاتورة

### إضافة طلب صيانة
1. انتقل إلى "نظام الصيانة"
2. اضغط على "استقبال جهاز"
3. املأ بيانات العميل والجهاز
4. احفظ واطبع إيصال الاستلام

## الميزات المتقدمة

### النسخ الاحتياطي
- يمكن إعداد نسخ احتياطي تلقائي من قاعدة البيانات
- تصدير البيانات إلى ملفات Excel

### الطباعة
- دعم الطابعات الحرارية (58mm, 80mm)
- دعم الطابعات العادية (A4, A5)
- تخصيص تصميم الفواتير

### التقارير
- تقارير تفصيلية للمبيعات
- تحليل الأرباح والخسائر
- إحصائيات الأداء

## الأمان

### حماية الملفات
- جميع ملفات PHP محمية من الوصول المباشر
- تشفير كلمات المرور
- جلسات آمنة

### صلاحيات المستخدمين
- **المدير:** صلاحيات كاملة
- **الموظف:** صلاحيات محدودة (البيع والاستعلام)

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```
تحقق من:
- بيانات الاتصال في config/database.php
- تشغيل خدمة MySQL
- صلاحيات المستخدم
```

#### مشكلة في الطباعة
```
تحقق من:
- إعدادات الطابعة في المتصفح
- تثبيت تعريفات الطابعة
- اتصال الطابعة
```

#### مشكلة في رفع الصور
```
تحقق من:
- صلاحيات مجلد uploads/
- حجم الملف المسموح
- نوع الملف المدعوم
```

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +964-XXX-XXXX

## الترخيص

هذا النظام مطور خصيصاً لإدارة المحلات التجارية.
جميع الحقوق محفوظة © 2024

## التحديثات

### الإصدار 1.0
- إطلاق النظام الأساسي
- نقطة البيع
- إدارة المنتجات والعملاء
- نظام الصيانة الأساسي

### التحديثات المستقبلية
- تطبيق موبايل
- تكامل مع أنظمة الدفع الإلكتروني
- تقارير متقدمة
- نظام إشعارات WhatsApp

---

**ملاحظة:** هذا النظام قيد التطوير المستمر. يرجى التحقق من التحديثات بانتظام.
