# نظام إدارة المحل المتكامل - TechZone

نظام شامل ومتطور لإدارة المحلات التجارية يشمل نقطة البيع، إدارة المنتجات، العملاء، الموردين، ونظام الصيانة مع واجهة عملاء احترافية.

## 🌟 المميزات الرئيسية

### 🌐 واجهة العملاء (الموقع الرئيسي)
- **تصميم عصري ومتجاوب** مع جميع الأجهزة
- **عرض المنتجات** بشكل جذاب مع إمكانية الفلترة
- **نظام سلة التسوق** المتقدم
- **طلب الصيانة أونلاين** مع نموذج تفصيلي
- **نظام التواصل** المباشر عبر واتساب
- **الوضع الليلي/النهاري** لراحة العين
- **تأثيرات بصرية جميلة** وحركات سلسة

### 🏪 نقطة البيع (POS)
- واجهة سهلة الاستخدام لنقطة البيع
- البحث عن المنتجات بالاسم أو الباركود
- دعم قارئ الباركود
- إضافة خصم للمنتج أو للفاتورة
- دعم البيع النقدي والآجل
- طباعة الفواتير الاحترافية

### 📦 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تصنيف المنتجات (موبايل، اكسسوارات، قطع غيار)
- إدارة المخزون والتنبيه عند النفاد
- استيراد وتصدير المنتجات من/إلى Excel
- إدارة الصور والباركود
- نظام البحث والفلترة المتقدم

### 👥 إدارة العملاء
- تسجيل بيانات العملاء الشاملة
- متابعة الديون والمدفوعات
- كشف حساب العميل التفصيلي
- تاريخ المشتريات والمعاملات
- نظام إشعارات الديون

### 🚚 إدارة الموردين
- تسجيل بيانات الموردين
- متابعة المشتريات والمدفوعات
- كشف حساب المورد
- إدارة طلبات الشراء

### 🔧 نظام الصيانة المتطور
- **استقبال الأجهزة** مع تفاصيل شاملة
- **متابعة حالة الصيانة** (مستلم، قيد التصليح، جاهز، مسلم)
- **إدارة قطع الغيار** المستخدمة
- **طباعة إيصالات الاستلام** الاحترافية
- **تقدير التكلفة** والتكلفة الفعلية
- **ملاحظات الفني** التفصيلية

### 📊 لوحة التحكم والتقارير
- **لوحة تحكم تفاعلية** مع إحصائيات مباشرة
- **رسوم بيانية** للمبيعات والأرباح
- **تقارير شاملة** للمبيعات والصيانة
- **إحصائيات المخزون** والتنبيهات
- **تصدير التقارير** إلى Excel

## متطلبات التشغيل

- خادم ويب (Apache/Nginx)
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- متصفح ويب حديث

## 🚀 التثبيت والإعداد

### الطريقة السريعة (موصى بها)

1. **إنشاء قاعدة البيانات:**
```sql
CREATE DATABASE irjnpfzw_fajir CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'irjnpfzw_fajir'@'localhost' IDENTIFIED BY 'irjnpfzw_fajir';
GRANT ALL PRIVILEGES ON irjnpfzw_fajir.* TO 'irjnpfzw_fajir'@'localhost';
FLUSH PRIVILEGES;
```

2. **رفع الملفات:**
- ارفع مجلد `irjnpfzw_fajir` إلى خادم الويب
- تأكد من أن المجلد قابل للقراءة من قبل خادم الويب

3. **تشغيل التحديث التلقائي:**
- افتح المتصفح وانتقل إلى: `http://your-domain.com/irjnpfzw_fajir/update_database.php`
- سيتم إنشاء جميع الجداول وإضافة البيانات التجريبية تلقائياً

4. **إعداد الصلاحيات (اختياري):**
```bash
chmod 755 irjnpfzw_fajir/
chmod 644 irjnpfzw_fajir/*.php
chmod 755 irjnpfzw_fajir/assets/
mkdir irjnpfzw_fajir/uploads && chmod 755 irjnpfzw_fajir/uploads/
```

### الطريقة اليدوية

1. **إنشاء قاعدة البيانات كما هو موضح أعلاه**

2. **استيراد الجداول:**
```bash
mysql -u irjnpfzw_fajir -p irjnpfzw_fajir < database.sql
```

3. **رفع الملفات وإعداد الصلاحيات كما هو موضح أعلاه**

## بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** password

⚠️ **مهم:** يرجى تغيير كلمة المرور فور تسجيل الدخول الأول

## 📖 دليل الاستخدام

### 🌐 للعملاء (الواجهة الرئيسية)

1. **تصفح المنتجات:**
   - افتح الموقع: `http://your-domain.com/irjnpfzw_fajir/`
   - تصفح المنتجات حسب الفئات
   - استخدم البحث للعثور على منتج معين

2. **طلب منتجات:**
   - أضف المنتجات لسلة التسوق
   - راجع السلة وأكمل الطلب
   - تواصل عبر واتساب لإتمام الشراء

3. **طلب صيانة:**
   - انتقل لقسم "طلب صيانة"
   - املأ بيانات الجهاز والمشكلة
   - أرسل الطلب وانتظر التواصل

### 🔐 للإدارة (لوحة التحكم)

1. **تسجيل الدخول:**
   - انتقل إلى: `http://your-domain.com/irjnpfzw_fajir/auth/login.php`
   - أدخل بيانات الدخول (admin/password)
   - ستظهر لك لوحة التحكم

2. **إدارة المنتجات:**
   - انتقل إلى "إدارة المنتجات"
   - أضف منتجات جديدة أو عدل الموجودة
   - راقب مستويات المخزون

3. **نقطة البيع:**
   - انتقل إلى "نقطة البيع"
   - ابحث عن المنتجات وأضفها للفاتورة
   - اختر العميل وطريقة الدفع
   - احفظ واطبع الفاتورة

4. **نظام الصيانة:**
   - انتقل إلى "نظام الصيانة"
   - استقبل الأجهزة وسجل المشاكل
   - تابع حالة الإصلاح
   - اطبع إيصالات الاستلام والتسليم

5. **إدارة العملاء:**
   - سجل بيانات العملاء
   - تابع الديون والمدفوعات
   - اعرض كشوف الحسابات

## 🔧 الميزات التقنية

### 🎨 التصميم والواجهة
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **ألوان احترافية** (أزرق ملكي، رمادي داكن، أبيض)
- **خط Cairo** للنصوص العربية
- **تأثيرات CSS3** وحركات سلسة
- **وضع ليلي/نهاري** قابل للتبديل

### 💻 التقنيات المستخدمة
- **Backend:** PHP 7.4+ مع PDO
- **Frontend:** HTML5, CSS3, JavaScript ES6+
- **Database:** MySQL 5.7+ مع UTF8MB4
- **Icons:** Font Awesome 6.0
- **Charts:** Chart.js للرسوم البيانية
- **Animations:** Animate.css

### 🔒 الأمان والحماية
- **تشفير كلمات المرور** باستخدام password_hash()
- **حماية من SQL Injection** باستخدام Prepared Statements
- **تنظيف البيانات** قبل الحفظ
- **جلسات آمنة** مع انتهاء صلاحية
- **صلاحيات المستخدمين** (مدير/موظف)

### 📱 التوافق والاستجابة
- **متوافق مع الهواتف** والأجهزة اللوحية
- **تصميم متجاوب** لجميع أحجام الشاشات
- **سرعة تحميل عالية** مع تحسين الأداء
- **دعم اللمس** للأجهزة التي تدعمه

### 🚀 الأداء والتحسين
- **استعلامات محسنة** لقاعدة البيانات
- **تحميل تدريجي** للبيانات (Pagination)
- **ضغط الصور** التلقائي
- **تخزين مؤقت** للبيانات المتكررة

## 📊 الميزات المتقدمة

### 💾 النسخ الاحتياطي
- نسخ احتياطي تلقائي من قاعدة البيانات
- تصدير البيانات إلى ملفات Excel
- استيراد البيانات من ملفات Excel

### 🖨️ الطباعة
- دعم الطابعات الحرارية (58mm, 80mm)
- دعم الطابعات العادية (A4, A5)
- تخصيص تصميم الفواتير والإيصالات
- طباعة كشوف الحسابات

### 📈 التقارير والإحصائيات
- تقارير تفصيلية للمبيعات اليومية والشهرية
- تحليل الأرباح والخسائر
- إحصائيات الأداء والمخزون
- رسوم بيانية تفاعلية

## الأمان

### حماية الملفات
- جميع ملفات PHP محمية من الوصول المباشر
- تشفير كلمات المرور
- جلسات آمنة

### صلاحيات المستخدمين
- **المدير:** صلاحيات كاملة
- **الموظف:** صلاحيات محدودة (البيع والاستعلام)

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```
تحقق من:
- بيانات الاتصال في config/database.php
- تشغيل خدمة MySQL
- صلاحيات المستخدم
```

#### مشكلة في الطباعة
```
تحقق من:
- إعدادات الطابعة في المتصفح
- تثبيت تعريفات الطابعة
- اتصال الطابعة
```

#### مشكلة في رفع الصور
```
تحقق من:
- صلاحيات مجلد uploads/
- حجم الملف المسموح
- نوع الملف المدعوم
```

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +964-XXX-XXXX

## الترخيص

هذا النظام مطور خصيصاً لإدارة المحلات التجارية.
جميع الحقوق محفوظة © 2024

## التحديثات

### الإصدار 1.0
- إطلاق النظام الأساسي
- نقطة البيع
- إدارة المنتجات والعملاء
- نظام الصيانة الأساسي

### 🔮 التحديثات المستقبلية
- **تطبيق موبايل** للأندرويد و iOS
- **تكامل مع أنظمة الدفع الإلكتروني** (فيزا، ماستركارد)
- **تقارير متقدمة** مع ذكاء اصطناعي
- **نظام إشعارات WhatsApp** التلقائي
- **نظام CRM** متقدم لإدارة العملاء
- **تكامل مع المحاسبة** والضرائب

## 📁 هيكل المشروع

```
irjnpfzw_fajir/
├── 📁 admin/                 # لوحة التحكم الإدارية
│   └── dashboard.php         # الصفحة الرئيسية للإدارة
├── 📁 api/                   # واجهات برمجة التطبيقات
│   ├── products.php          # API المنتجات
│   ├── customers.php         # API العملاء
│   ├── sales.php            # API المبيعات
│   ├── maintenance.php      # API الصيانة
│   └── ...                  # ملفات API أخرى
├── 📁 assets/               # الملفات الثابتة
│   ├── 📁 css/              # ملفات التصميم
│   │   ├── main.css         # التصميم الرئيسي
│   │   ├── customer.css     # تصميم واجهة العملاء
│   │   ├── admin.css        # تصميم لوحة التحكم
│   │   └── ...              # ملفات CSS أخرى
│   ├── 📁 js/               # ملفات JavaScript
│   │   ├── main.js          # الوظائف الرئيسية
│   │   ├── customer.js      # وظائف واجهة العملاء
│   │   ├── admin.js         # وظائف لوحة التحكم
│   │   └── ...              # ملفات JS أخرى
│   └── 📁 images/           # الصور والأيقونات
├── 📁 auth/                 # نظام المصادقة
│   ├── login.php            # صفحة تسجيل الدخول
│   └── logout.php           # تسجيل الخروج
├── 📁 config/               # ملفات التكوين
│   ├── database.php         # إعدادات قاعدة البيانات
│   └── functions.php        # الوظائف المساعدة
├── 📁 customers/            # إدارة العملاء
├── 📁 maintenance/          # نظام الصيانة
├── 📁 pos/                  # نقطة البيع
├── 📁 products/             # إدارة المنتجات
├── 📁 reports/              # التقارير
├── 📁 suppliers/            # إدارة الموردين
├── 📁 uploads/              # الملفات المرفوعة
├── index.php                # الصفحة الرئيسية للعملاء
├── database.sql             # ملف قاعدة البيانات
├── update_database.php      # تحديث قاعدة البيانات
└── README.md                # دليل المشروع
```

## 🤝 المساهمة والدعم

### المساهمة في المشروع
نرحب بمساهماتكم لتطوير هذا النظام:
1. **Fork** المشروع
2. أنشئ **branch** جديد للميزة
3. **Commit** التغييرات
4. **Push** للـ branch
5. أنشئ **Pull Request**

### الإبلاغ عن المشاكل
- استخدم **Issues** في GitHub
- اذكر تفاصيل المشكلة والخطوات لإعادة إنتاجها
- أرفق صور الشاشة إن أمكن

### طلب ميزات جديدة
- اقترح الميزات الجديدة عبر **Issues**
- اشرح الفائدة من الميزة المقترحة
- قدم أمثلة على الاستخدام

## 📞 التواصل والدعم

- **GitHub Issues:** للمشاكل التقنية
- **Email:** للاستفسارات العامة
- **WhatsApp:** للدعم السريع

---

**ملاحظة:** هذا النظام مطور بعناية فائقة ليكون شاملاً ومتطوراً. نحن ملتزمون بالتطوير المستمر وإضافة المزيد من الميزات.
