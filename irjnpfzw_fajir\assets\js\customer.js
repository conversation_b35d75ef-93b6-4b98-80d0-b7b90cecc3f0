// واجهة العملاء - Customer Interface
const CustomerSite = {
    cart: [],
    currentTheme: 'light',

    // تهيئة الموقع
    init() {
        this.bindEvents();
        this.initScrollEffects();
        this.initTheme();
        this.loadCart();
        this.initAnimations();
    },

    // ربط الأحداث
    bindEvents() {
        // التنقل السلس
        document.querySelectorAll('.nav-link[href^="#"]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                
                // تحديث الرابط النشط
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                link.classList.add('active');
            });
        });

        // فلترة المنتجات
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const category = e.target.dataset.category;
                this.filterProducts(category);
                
                // تحديث الزر النشط
                document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });

        // إضافة للسلة
        document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const productId = e.target.dataset.productId;
                this.addToCart(productId);
            });
        });

        // عرض سريع للمنتج
        document.querySelectorAll('.quick-view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const productId = e.target.dataset.productId;
                this.showProductModal(productId);
            });
        });

        // سلة التسوق
        document.getElementById('cart-btn').addEventListener('click', () => {
            this.toggleCart();
        });

        document.getElementById('close-cart').addEventListener('click', () => {
            this.closeCart();
        });

        // تبديل الثيم
        document.getElementById('theme-toggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // زر العودة للأعلى
        document.getElementById('scroll-top').addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // نماذج الاتصال
        document.getElementById('contact-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitContactForm(e.target);
        });

        document.getElementById('maintenance-request-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitMaintenanceRequest(e.target);
        });

        // إغلاق النوافذ المنبثقة
        document.getElementById('close-product-modal').addEventListener('click', () => {
            this.closeProductModal();
        });

        // إغلاق النوافذ عند النقر خارجها
        document.getElementById('product-modal').addEventListener('click', (e) => {
            if (e.target.id === 'product-modal') {
                this.closeProductModal();
            }
        });

        // القائمة المحمولة
        document.getElementById('mobile-menu-toggle').addEventListener('click', () => {
            this.toggleMobileMenu();
        });
    },

    // تأثيرات التمرير
    initScrollEffects() {
        // تأثير الهيدر عند التمرير
        window.addEventListener('scroll', () => {
            const header = document.getElementById('main-header');
            const scrollTop = document.getElementById('scroll-top');
            
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
                scrollTop.classList.add('visible');
            } else {
                header.classList.remove('scrolled');
                scrollTop.classList.remove('visible');
            }
        });

        // تحديث الرابط النشط حسب القسم
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link[href^="#"]');

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.scrollY >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });
    },

    // تهيئة الثيم
    initTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.currentTheme = savedTheme;
        document.body.classList.toggle('dark-mode', savedTheme === 'dark');
        
        const themeIcon = document.querySelector('#theme-toggle i');
        themeIcon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    },

    // تبديل الثيم
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.body.classList.toggle('dark-mode');
        localStorage.setItem('theme', this.currentTheme);
        
        const themeIcon = document.querySelector('#theme-toggle i');
        themeIcon.className = this.currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    },

    // تهيئة الحركات
    initAnimations() {
        // مراقب التقاطع للحركات
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // إضافة العناصر للمراقبة
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    },

    // فلترة المنتجات
    filterProducts(category) {
        const products = document.querySelectorAll('.product-card');
        
        products.forEach(product => {
            if (category === 'all' || product.dataset.category === category) {
                product.style.display = 'block';
                product.style.animation = 'fadeInUp 0.5s ease-out';
            } else {
                product.style.display = 'none';
            }
        });
    },

    // إضافة منتج للسلة
    async addToCart(productId) {
        try {
            // جلب تفاصيل المنتج
            const response = await fetch(`api/products.php?id=${productId}`);
            const product = await response.json();
            
            if (!response.ok) {
                throw new Error(product.error || 'خطأ في جلب المنتج');
            }

            // التحقق من وجود المنتج في السلة
            const existingItem = this.cart.find(item => item.id === productId);
            
            if (existingItem) {
                if (existingItem.quantity < product.quantity) {
                    existingItem.quantity++;
                    existingItem.total = existingItem.quantity * existingItem.price;
                } else {
                    showMessage('الكمية المطلوبة غير متوفرة', 'warning');
                    return;
                }
            } else {
                this.cart.push({
                    id: product.id,
                    name: product.name,
                    price: product.selling_price,
                    quantity: 1,
                    total: product.selling_price,
                    image: product.image
                });
            }

            this.updateCartUI();
            this.saveCart();
            showMessage('تم إضافة المنتج للسلة', 'success', 2000);
            
        } catch (error) {
            console.error('Error adding to cart:', error);
            showMessage('خطأ في إضافة المنتج للسلة', 'error');
        }
    },

    // تحديث واجهة السلة
    updateCartUI() {
        const cartCount = document.getElementById('cart-count');
        const cartItems = document.getElementById('cart-items');
        const cartTotal = document.getElementById('cart-total');
        
        // تحديث العدد
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
        cartCount.style.display = totalItems > 0 ? 'flex' : 'none';
        
        // تحديث العناصر
        if (this.cart.length === 0) {
            cartItems.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">السلة فارغة</p>';
        } else {
            cartItems.innerHTML = this.cart.map(item => `
                <div class="cart-item">
                    <img src="${item.image || 'assets/images/no-image.png'}" alt="${item.name}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
                    <div class="cart-item-info" style="flex: 1; margin: 0 10px;">
                        <h4 style="font-size: 0.9rem; margin-bottom: 5px;">${item.name}</h4>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <button onclick="CustomerSite.updateCartQuantity(${item.id}, ${item.quantity - 1})" style="background: #dc3545; color: white; border: none; width: 25px; height: 25px; border-radius: 50%; cursor: pointer;">-</button>
                            <span>${item.quantity}</span>
                            <button onclick="CustomerSite.updateCartQuantity(${item.id}, ${item.quantity + 1})" style="background: #28a745; color: white; border: none; width: 25px; height: 25px; border-radius: 50%; cursor: pointer;">+</button>
                        </div>
                        <div style="font-weight: 600; color: #0057D9;">${formatMoney(item.total)}</div>
                    </div>
                    <button onclick="CustomerSite.removeFromCart(${item.id})" style="background: #dc3545; color: white; border: none; padding: 5px; border-radius: 4px; cursor: pointer;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        }
        
        // تحديث المجموع
        const total = this.cart.reduce((sum, item) => sum + item.total, 0);
        cartTotal.textContent = formatMoney(total);
    },

    // تحديث كمية المنتج في السلة
    updateCartQuantity(productId, newQuantity) {
        if (newQuantity <= 0) {
            this.removeFromCart(productId);
            return;
        }
        
        const item = this.cart.find(item => item.id === productId);
        if (item) {
            item.quantity = newQuantity;
            item.total = item.quantity * item.price;
            this.updateCartUI();
            this.saveCart();
        }
    },

    // حذف منتج من السلة
    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.id !== productId);
        this.updateCartUI();
        this.saveCart();
        showMessage('تم حذف المنتج من السلة', 'info');
    },

    // فتح/إغلاق السلة
    toggleCart() {
        const cartSidebar = document.getElementById('cart-sidebar');
        cartSidebar.classList.toggle('open');
    },

    closeCart() {
        const cartSidebar = document.getElementById('cart-sidebar');
        cartSidebar.classList.remove('open');
    },

    // حفظ السلة
    saveCart() {
        localStorage.setItem('cart', JSON.stringify(this.cart));
    },

    // تحميل السلة
    loadCart() {
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
            this.cart = JSON.parse(savedCart);
            this.updateCartUI();
        }
    },

    // عرض نافذة المنتج
    async showProductModal(productId) {
        try {
            const response = await fetch(`api/products.php?id=${productId}`);
            const product = await response.json();
            
            if (!response.ok) {
                throw new Error(product.error || 'خطأ في جلب المنتج');
            }

            const modal = document.getElementById('product-modal');
            const details = document.getElementById('product-details');
            
            const categories = {
                'mobile': 'موبايل',
                'accessories': 'اكسسوارات',
                'parts': 'قطع غيار'
            };
            
            details.innerHTML = `
                <div style="padding: 30px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; align-items: start;">
                        <div>
                            <img src="${product.image || 'assets/images/no-image.png'}" alt="${product.name}" style="width: 100%; border-radius: 12px;">
                        </div>
                        <div>
                            <h2 style="font-size: 2rem; margin-bottom: 15px; color: #1C1C1E;">${product.name}</h2>
                            <div style="background: rgba(0, 87, 217, 0.1); color: #0057D9; padding: 8px 15px; border-radius: 20px; display: inline-block; margin-bottom: 20px; font-weight: 600;">
                                ${categories[product.category] || product.category}
                            </div>
                            <div style="font-size: 2rem; font-weight: 800; color: #0057D9; margin-bottom: 20px;">
                                ${formatMoney(product.selling_price)}
                            </div>
                            <div style="margin-bottom: 20px;">
                                <strong>المتوفر:</strong> ${product.quantity} قطعة
                            </div>
                            ${product.description ? `<div style="margin-bottom: 20px; line-height: 1.6;"><strong>الوصف:</strong><br>${product.description}</div>` : ''}
                            ${product.barcode ? `<div style="margin-bottom: 20px;"><strong>الباركود:</strong> <code style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px;">${product.barcode}</code></div>` : ''}
                            <button onclick="CustomerSite.addToCart(${product.id}); CustomerSite.closeProductModal();" style="width: 100%; background: linear-gradient(135deg, #0057D9 0%, #0041a3 100%); color: white; border: none; padding: 15px; border-radius: 12px; font-weight: 600; font-size: 1.1rem; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 10px;">
                                <i class="fas fa-cart-plus"></i> إضافة للسلة
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            modal.classList.add('open');
            
        } catch (error) {
            console.error('Error showing product:', error);
            showMessage('خطأ في عرض المنتج', 'error');
        }
    },

    // إغلاق نافذة المنتج
    closeProductModal() {
        const modal = document.getElementById('product-modal');
        modal.classList.remove('open');
    },

    // إرسال نموذج التواصل
    async submitContactForm(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        try {
            const response = await fetch('api/contact.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage('تم إرسال رسالتك بنجاح. سنتواصل معك قريباً', 'success');
                form.reset();
            } else {
                throw new Error(result.error || 'خطأ في إرسال الرسالة');
            }
        } catch (error) {
            console.error('Error submitting contact form:', error);
            showMessage('خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى', 'error');
        }
    },

    // إرسال طلب صيانة
    async submitMaintenanceRequest(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        try {
            const response = await fetch('api/maintenance_request.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage('تم إرسال طلب الصيانة بنجاح. سنتواصل معك لتحديد موعد الاستلام', 'success');
                form.reset();
            } else {
                throw new Error(result.error || 'خطأ في إرسال طلب الصيانة');
            }
        } catch (error) {
            console.error('Error submitting maintenance request:', error);
            showMessage('خطأ في إرسال طلب الصيانة. يرجى المحاولة مرة أخرى', 'error');
        }
    },

    // تبديل القائمة المحمولة
    toggleMobileMenu() {
        const nav = document.querySelector('.main-nav');
        nav.classList.toggle('mobile-open');
    }
};
