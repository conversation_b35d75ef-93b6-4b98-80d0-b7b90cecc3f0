<?php
// ملف فحص النظام
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>فحص حالة النظام</h1>";

// فحص إصدار PHP
echo "<h2>1. إصدار PHP</h2>";
echo "الإصدار الحالي: " . PHP_VERSION . "<br>";
if (version_compare(PHP_VERSION, '7.4.0') >= 0) {
    echo "✅ إصدار PHP مناسب<br>";
} else {
    echo "❌ يتطلب PHP 7.4 أو أحدث<br>";
}

// فحص الامتدادات المطلوبة
echo "<h2>2. الامتدادات المطلوبة</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext متوفر<br>";
    } else {
        echo "❌ $ext غير متوفر<br>";
    }
}

// فحص الملفات
echo "<h2>3. فحص الملفات الأساسية</h2>";
$required_files = [
    'config/database.php',
    'assets/css/customer.css',
    'assets/js/customer.js',
    'auth/login.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

// فحص الصلاحيات
echo "<h2>4. فحص الصلاحيات</h2>";
$directories = ['.', 'assets', 'uploads'];
foreach ($directories as $dir) {
    if (is_readable($dir)) {
        echo "✅ $dir قابل للقراءة<br>";
    } else {
        echo "❌ $dir غير قابل للقراءة<br>";
    }
    
    if (is_writable($dir)) {
        echo "✅ $dir قابل للكتابة<br>";
    } else {
        echo "⚠️ $dir غير قابل للكتابة<br>";
    }
}

// فحص قاعدة البيانات
echo "<h2>5. فحص قاعدة البيانات</h2>";
try {
    require_once 'config/database.php';
    echo "✅ الاتصال بقاعدة البيانات نجح<br>";
    
    // فحص الجداول
    $tables = ['users', 'products', 'customers', 'sales'];
    foreach ($tables as $table) {
        try {
            $result = fetchOne("SELECT COUNT(*) as count FROM $table");
            echo "✅ جدول $table موجود ويحتوي على " . $result['count'] . " سجل<br>";
        } catch (Exception $e) {
            echo "❌ جدول $table غير موجود أو به مشكلة<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
    echo "<p><strong>الحل:</strong> قم بتشغيل <a href='update_database.php'>update_database.php</a> لإنشاء قاعدة البيانات</p>";
}

// معلومات الخادم
echo "<h2>6. معلومات الخادم</h2>";
echo "نظام التشغيل: " . PHP_OS . "<br>";
echo "خادم الويب: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "المجلد الحالي: " . __DIR__ . "<br>";
echo "الرابط: " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . "<br>";

// اختبار بسيط للصفحة الرئيسية
echo "<h2>7. اختبار الصفحة الرئيسية</h2>";
try {
    ob_start();
    include 'index.php';
    $output = ob_get_clean();
    
    if (strlen($output) > 1000) {
        echo "✅ الصفحة الرئيسية تعمل بشكل صحيح<br>";
    } else {
        echo "⚠️ الصفحة الرئيسية قد تحتوي على مشاكل<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في الصفحة الرئيسية: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>الخطوات التالية:</h2>";
echo "<ol>";
echo "<li><a href='update_database.php'>إنشاء/تحديث قاعدة البيانات</a></li>";
echo "<li><a href='index.php'>اختبار الصفحة الرئيسية</a></li>";
echo "<li><a href='auth/login.php'>تسجيل الدخول للإدارة</a></li>";
echo "</ol>";

echo "<p><strong>بيانات الدخول الافتراضية:</strong><br>";
echo "اسم المستخدم: admin<br>";
echo "كلمة المرور: password</p>";
?>
