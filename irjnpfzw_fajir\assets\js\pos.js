// نظام نقطة البيع
const POS = {
    cart: [],
    products: [],
    customers: [],
    currentInvoice: null,

    // تهيئة النظام
    init() {
        this.loadProducts();
        this.loadCustomers();
        this.bindEvents();
        this.updateInvoice();
    },

    // ربط الأحداث
    bindEvents() {
        // البحث عن المنتجات
        document.getElementById('product-search').addEventListener('input', (e) => {
            this.searchProducts(e.target.value);
        });

        // فلترة المنتجات حسب الفئة
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.filterProducts(e.target.dataset.category);
            });
        });

        // مسح الباركود
        document.getElementById('barcode-scan').addEventListener('click', () => {
            this.scanBarcode();
        });

        // إضافة عميل جديد
        document.getElementById('add-customer').addEventListener('click', () => {
            this.showCustomerModal();
        });

        // حفظ عميل جديد
        document.getElementById('customer-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCustomer();
        });

        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                this.hideModals();
            });
        });

        // تحديث الخصم
        document.getElementById('discount').addEventListener('input', () => {
            this.updateInvoice();
        });

        document.getElementById('discount-type').addEventListener('change', () => {
            this.updateInvoice();
        });

        // تحديث المبلغ المدفوع
        document.getElementById('paid-amount').addEventListener('input', () => {
            this.updateChange();
        });

        // نوع الدفع
        document.querySelectorAll('input[name="payment-type"]').forEach(radio => {
            radio.addEventListener('change', () => {
                this.togglePaymentType();
            });
        });

        // أزرار العمليات
        document.getElementById('clear-invoice').addEventListener('click', () => {
            this.clearInvoice();
        });

        document.getElementById('save-invoice').addEventListener('click', () => {
            this.saveInvoice();
        });

        document.getElementById('print-invoice').addEventListener('click', () => {
            this.printInvoice();
        });
    },

    // تحميل المنتجات
    async loadProducts() {
        try {
            const response = await fetch('../api/products.php');
            this.products = await response.json();
            this.displayProducts();
        } catch (error) {
            console.error('Error loading products:', error);
            showMessage('خطأ في تحميل المنتجات', 'error');
        }
    },

    // تحميل العملاء
    async loadCustomers() {
        try {
            const response = await fetch('../api/customers.php');
            this.customers = await response.json();
            this.populateCustomerSelect();
        } catch (error) {
            console.error('Error loading customers:', error);
        }
    },

    // عرض المنتجات
    displayProducts(products = this.products) {
        const grid = document.getElementById('products-grid');
        grid.innerHTML = '';

        products.forEach(product => {
            const card = document.createElement('div');
            card.className = 'product-card';
            card.innerHTML = `
                <img src="${product.image || '../assets/images/no-image.png'}" alt="${product.name}">
                <div class="name">${product.name}</div>
                <div class="price">${formatMoney(product.selling_price)}</div>
                <div class="stock">المخزون: ${product.quantity}</div>
            `;
            
            card.addEventListener('click', () => {
                this.addToCart(product);
            });
            
            grid.appendChild(card);
        });
    },

    // البحث عن المنتجات
    searchProducts(query) {
        const filtered = this.products.filter(product => 
            product.name.toLowerCase().includes(query.toLowerCase()) ||
            product.barcode.includes(query)
        );
        this.displayProducts(filtered);
    },

    // فلترة المنتجات
    filterProducts(category) {
        if (category === 'all') {
            this.displayProducts();
        } else {
            const filtered = this.products.filter(product => product.category === category);
            this.displayProducts(filtered);
        }
    },

    // مسح الباركود
    scanBarcode() {
        // هنا يمكن إضافة مكتبة قراءة الباركود
        const barcode = prompt('أدخل الباركود:');
        if (barcode) {
            const product = this.products.find(p => p.barcode === barcode);
            if (product) {
                this.addToCart(product);
            } else {
                showMessage('المنتج غير موجود', 'error');
            }
        }
    },

    // إضافة منتج للسلة
    addToCart(product) {
        if (product.quantity <= 0) {
            showMessage('المنتج غير متوفر في المخزون', 'error');
            return;
        }

        const existingItem = this.cart.find(item => item.id === product.id);
        
        if (existingItem) {
            if (existingItem.quantity < product.quantity) {
                existingItem.quantity++;
                existingItem.total = existingItem.quantity * existingItem.price;
            } else {
                showMessage('الكمية المطلوبة غير متوفرة', 'error');
                return;
            }
        } else {
            this.cart.push({
                id: product.id,
                name: product.name,
                price: product.selling_price,
                quantity: 1,
                total: product.selling_price,
                maxQuantity: product.quantity
            });
        }

        this.updateInvoiceItems();
        this.updateInvoice();
        showMessage('تم إضافة المنتج', 'success', 1000);
    },

    // تحديث عناصر الفاتورة
    updateInvoiceItems() {
        const itemsList = document.getElementById('invoice-items');
        itemsList.innerHTML = '';

        this.cart.forEach((item, index) => {
            const itemDiv = document.createElement('div');
            itemDiv.className = 'invoice-item';
            itemDiv.innerHTML = `
                <div class="item-name">${item.name}</div>
                <input type="number" class="quantity-input" value="${item.quantity}" 
                       min="1" max="${item.maxQuantity}" 
                       onchange="POS.updateQuantity(${index}, this.value)">
                <div>${formatMoney(item.price)}</div>
                <div>${formatMoney(item.total)}</div>
                <button class="remove-item" onclick="POS.removeFromCart(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            itemsList.appendChild(itemDiv);
        });
    },

    // تحديث الكمية
    updateQuantity(index, newQuantity) {
        const item = this.cart[index];
        const quantity = parseInt(newQuantity);
        
        if (quantity <= 0) {
            this.removeFromCart(index);
            return;
        }
        
        if (quantity > item.maxQuantity) {
            showMessage('الكمية المطلوبة غير متوفرة', 'error');
            this.updateInvoiceItems();
            return;
        }
        
        item.quantity = quantity;
        item.total = item.quantity * item.price;
        this.updateInvoice();
    },

    // حذف من السلة
    removeFromCart(index) {
        this.cart.splice(index, 1);
        this.updateInvoiceItems();
        this.updateInvoice();
    },

    // تحديث الفاتورة
    updateInvoice() {
        const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
        const discount = this.calculateDiscount(subtotal);
        const total = subtotal - discount;

        document.getElementById('subtotal').textContent = formatMoney(subtotal);
        document.getElementById('total').textContent = formatMoney(total);
        
        // تحديث المبلغ المدفوع إذا كان نقدي
        const paymentType = document.querySelector('input[name="payment-type"]:checked').value;
        if (paymentType === 'cash') {
            document.getElementById('paid-amount').value = total;
            this.updateChange();
        }
    },

    // حساب الخصم
    calculateDiscount(subtotal) {
        const discountValue = parseFloat(document.getElementById('discount').value) || 0;
        const discountType = document.getElementById('discount-type').value;
        
        if (discountType === 'percent') {
            return (subtotal * discountValue) / 100;
        } else {
            return discountValue;
        }
    },

    // تحديث الباقي
    updateChange() {
        const total = this.getTotal();
        const paid = parseFloat(document.getElementById('paid-amount').value) || 0;
        const change = paid - total;
        
        document.getElementById('change').textContent = formatMoney(Math.max(0, change));
    },

    // الحصول على المجموع الكلي
    getTotal() {
        const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
        const discount = this.calculateDiscount(subtotal);
        return subtotal - discount;
    },

    // تبديل نوع الدفع
    togglePaymentType() {
        const paymentType = document.querySelector('input[name="payment-type"]:checked').value;
        const cashPayment = document.getElementById('cash-payment');
        
        if (paymentType === 'cash') {
            cashPayment.style.display = 'flex';
            document.getElementById('paid-amount').value = this.getTotal();
            this.updateChange();
        } else {
            cashPayment.style.display = 'none';
        }
    },

    // عرض نافذة إضافة عميل
    showCustomerModal() {
        document.getElementById('customer-modal').classList.add('show');
    },

    // إخفاء النوافذ المنبثقة
    hideModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
        });
    },

    // حفظ عميل جديد
    async saveCustomer() {
        const form = document.getElementById('customer-form');
        const formData = new FormData(form);
        
        try {
            const response = await fetch('../api/customers.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                showMessage('تم حفظ العميل بنجاح', 'success');
                this.loadCustomers();
                this.hideModals();
                form.reset();
            } else {
                showMessage(result.message || 'خطأ في حفظ العميل', 'error');
            }
        } catch (error) {
            console.error('Error saving customer:', error);
            showMessage('خطأ في حفظ العميل', 'error');
        }
    },

    // ملء قائمة العملاء
    populateCustomerSelect() {
        const select = document.getElementById('customer-select');
        select.innerHTML = '<option value="">عميل نقدي</option>';
        
        this.customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            select.appendChild(option);
        });
    },

    // مسح الفاتورة
    async clearInvoice() {
        const confirmed = await confirmDelete('هل تريد مسح الفاتورة؟');
        if (confirmed) {
            this.cart = [];
            this.updateInvoiceItems();
            this.updateInvoice();
            document.getElementById('customer-select').value = '';
            document.getElementById('discount').value = 0;
            document.getElementById('paid-amount').value = 0;
            showMessage('تم مسح الفاتورة', 'info');
        }
    },

    // حفظ الفاتورة
    async saveInvoice() {
        if (this.cart.length === 0) {
            showMessage('لا توجد منتجات في الفاتورة', 'error');
            return;
        }

        const paymentType = document.querySelector('input[name="payment-type"]:checked').value;
        const customerId = document.getElementById('customer-select').value;
        
        if (paymentType === 'credit' && !customerId) {
            showMessage('يجب اختيار عميل للبيع الآجل', 'error');
            return;
        }

        const invoiceData = {
            customer_id: customerId || null,
            payment_type: paymentType,
            items: this.cart,
            subtotal: this.cart.reduce((sum, item) => sum + item.total, 0),
            discount: this.calculateDiscount(this.cart.reduce((sum, item) => sum + item.total, 0)),
            total: this.getTotal(),
            paid_amount: paymentType === 'cash' ? parseFloat(document.getElementById('paid-amount').value) : 0
        };

        try {
            const response = await fetch('../api/sales.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(invoiceData)
            });

            const result = await response.json();

            if (result.success) {
                showMessage('تم حفظ الفاتورة بنجاح', 'success');
                this.currentInvoice = result.invoice_id;
                // يمكن طباعة الفاتورة تلقائياً
                // this.printInvoice();
            } else {
                showMessage(result.message || 'خطأ في حفظ الفاتورة', 'error');
            }
        } catch (error) {
            console.error('Error saving invoice:', error);
            showMessage('خطأ في حفظ الفاتورة', 'error');
        }
    },

    // طباعة الفاتورة
    printInvoice() {
        if (this.cart.length === 0) {
            showMessage('لا توجد منتجات للطباعة', 'error');
            return;
        }

        // إنشاء نافذة طباعة
        const printWindow = window.open('', '_blank');
        const invoiceHTML = this.generateInvoiceHTML();
        
        printWindow.document.write(invoiceHTML);
        printWindow.document.close();
        printWindow.print();
    },

    // إنشاء HTML للفاتورة
    generateInvoiceHTML() {
        const customerName = document.getElementById('customer-select').selectedOptions[0]?.text || 'عميل نقدي';
        const invoiceNumber = document.getElementById('invoice-number').textContent;
        const now = new Date();
        
        let html = `
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة ${invoiceNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .invoice-info { margin-bottom: 20px; }
                    table { width: 100%; border-collapse: collapse; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    th { background-color: #f2f2f2; }
                    .total { font-weight: bold; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>اسم المحل</h1>
                    <p>العنوان - رقم الهاتف</p>
                </div>
                
                <div class="invoice-info">
                    <p><strong>رقم الفاتورة:</strong> ${invoiceNumber}</p>
                    <p><strong>التاريخ:</strong> ${formatDateTime(now)}</p>
                    <p><strong>العميل:</strong> ${customerName}</p>
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        this.cart.forEach(item => {
            html += `
                <tr>
                    <td>${item.name}</td>
                    <td>${item.quantity}</td>
                    <td>${formatMoney(item.price)}</td>
                    <td>${formatMoney(item.total)}</td>
                </tr>
            `;
        });
        
        const subtotal = this.cart.reduce((sum, item) => sum + item.total, 0);
        const discount = this.calculateDiscount(subtotal);
        const total = this.getTotal();
        
        html += `
                    </tbody>
                </table>
                
                <div style="margin-top: 20px; text-align: left;">
                    <p><strong>المجموع الفرعي: ${formatMoney(subtotal)}</strong></p>
                    <p><strong>الخصم: ${formatMoney(discount)}</strong></p>
                    <p class="total"><strong>المجموع الكلي: ${formatMoney(total)}</strong></p>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <p>شكراً لتعاملكم معنا</p>
                </div>
            </body>
            </html>
        `;
        
        return html;
    }
};
