/* صفحة إدارة المنتجات */

/* أدوات التحكم */
.controls-section {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.search-controls {
    display: flex;
    gap: 15px;
    flex: 1;
}

.search-box {
    display: flex;
    gap: 10px;
    min-width: 300px;
}

.search-box input {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: #1d4ed8;
}

.filter-controls {
    display: flex;
    gap: 10px;
}

.filter-controls select {
    padding: 10px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background: var(--white);
    min-width: 150px;
}

.action-controls {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-secondary {
    background: var(--gray-500);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-small {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* جدول المنتجات */
.products-table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 20px;
}

.products-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.products-table th {
    background: var(--gray-50);
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    border-bottom: 2px solid var(--gray-200);
    color: var(--gray-700);
}

.products-table td {
    padding: 12px 10px;
    text-align: center;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
}

.products-table tbody tr:hover {
    background: var(--gray-50);
}

.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 6px;
    border: 2px solid var(--gray-200);
}

.product-name {
    font-weight: 600;
    color: var(--dark-color);
    text-align: right;
    max-width: 200px;
}

.barcode {
    font-family: 'Courier New', monospace;
    background: var(--gray-100);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.category-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.category-mobile {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.category-accessories {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.category-parts {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.price {
    font-weight: 600;
    color: var(--success-color);
}

.quantity {
    font-weight: 600;
    font-size: 1.1rem;
}

.quantity.low {
    color: var(--warning-color);
}

.quantity.out {
    color: var(--danger-color);
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
}

.actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.action-btn {
    padding: 6px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.edit-btn {
    background: var(--primary-color);
    color: var(--white);
}

.delete-btn {
    background: var(--danger-color);
    color: var(--white);
}

.action-btn:hover {
    transform: scale(1.1);
}

/* التصفح */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    background: var(--white);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
}

.pagination button:hover {
    background: var(--gray-100);
}

.pagination button.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gray-50);
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-500);
    padding: 5px;
}

.close-modal:hover {
    color: var(--danger-color);
}

/* النماذج */
#product-form {
    padding: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--gray-700);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.barcode-input {
    display: flex;
    gap: 10px;
}

.barcode-input input {
    flex: 1;
}

.form-actions {
    padding: 20px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    background: var(--gray-50);
}

/* Responsive */
@media (max-width: 1024px) {
    .controls-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-controls {
        flex-direction: column;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filter-controls {
        flex-wrap: wrap;
    }
    
    .action-controls {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .products-table-container {
        overflow-x: auto;
    }
    
    .products-table {
        min-width: 800px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .controls-section {
        padding: 15px;
    }
    
    .btn {
        padding: 8px 15px;
        font-size: 0.8rem;
    }
}

/* تأثيرات التحميل */
.loading {
    text-align: center;
    padding: 40px;
    color: var(--gray-500);
}

.loading i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* رسائل فارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: var(--gray-300);
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--gray-600);
}

/* تنسيق خاص للأرقام */
.number-input {
    text-align: center;
}

/* تحسينات إضافية */
.highlight {
    background: rgba(255, 235, 59, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
}

.tooltip {
    position: relative;
    cursor: help;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dark-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1001;
}
