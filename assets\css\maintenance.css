/* صفحة نظام الصيانة */

/* إضافة ألوان خاصة بالصيانة */
:root {
    --received-color: #3b82f6;
    --progress-color: #f59e0b;
    --ready-color: #10b981;
    --delivered-color: #6b7280;
}

/* تخصيص بطاقات الإحصائيات للصيانة */
.stat-card.received {
    border-left-color: var(--received-color);
}

.stat-card.received .stat-icon {
    background: var(--received-color);
}

.stat-card.progress {
    border-left-color: var(--progress-color);
}

.stat-card.progress .stat-icon {
    background: var(--progress-color);
}

.stat-card.ready {
    border-left-color: var(--ready-color);
}

.stat-card.ready .stat-icon {
    background: var(--ready-color);
}

.stat-card.delivered {
    border-left-color: var(--delivered-color);
}

.stat-card.delivered .stat-icon {
    background: var(--delivered-color);
}

/* فلتر التاريخ */
.date-filter {
    padding: 12px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    background: var(--white);
    font-size: 1rem;
    min-width: 150px;
}

.date-filter:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* جدول الصيانة */
.maintenance-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.maintenance-table th {
    background: var(--gray-50);
    padding: 12px 8px;
    text-align: center;
    font-weight: 700;
    border-bottom: 2px solid var(--gray-200);
    color: var(--gray-700);
    white-space: nowrap;
    font-size: 0.85rem;
}

.maintenance-table td {
    padding: 12px 8px;
    text-align: center;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
}

.maintenance-table tbody tr:hover {
    background: var(--gray-50);
}

/* أرقام الطلبات */
.order-number {
    font-family: 'Courier New', monospace;
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* معلومات العميل */
.customer-name {
    font-weight: 600;
    color: var(--dark-color);
}

.customer-phone {
    font-family: 'Courier New', monospace;
    background: var(--gray-100);
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
    direction: ltr;
}

/* معلومات الجهاز */
.device-info {
    text-align: right;
}

.device-type {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 2px;
}

.device-model {
    font-size: 0.8rem;
    color: var(--gray-500);
}

/* وصف المشكلة */
.problem-description {
    max-width: 200px;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
}

.problem-description:hover {
    white-space: normal;
    overflow: visible;
    background: var(--white);
    box-shadow: var(--box-shadow);
    padding: 8px;
    border-radius: 4px;
    position: relative;
    z-index: 10;
}

/* التكلفة */
.cost-amount {
    font-weight: 700;
    font-size: 1rem;
}

.estimated-cost {
    color: var(--warning-color);
}

.actual-cost {
    color: var(--success-color);
}

/* حالات الصيانة */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-received {
    background: rgba(59, 130, 246, 0.1);
    color: var(--received-color);
}

.status-in_progress {
    background: rgba(245, 158, 11, 0.1);
    color: var(--progress-color);
}

.status-ready {
    background: rgba(16, 185, 129, 0.1);
    color: var(--ready-color);
}

.status-delivered {
    background: rgba(107, 114, 128, 0.1);
    color: var(--delivered-color);
}

/* التواريخ */
.date-info {
    font-size: 0.85rem;
    color: var(--gray-600);
}

.date-received {
    font-weight: 600;
}

.date-delivered {
    color: var(--success-color);
    font-weight: 600;
}

/* أزرار الإجراءات */
.actions {
    display: flex;
    gap: 6px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 6px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
}

.view-btn {
    background: var(--primary-color);
    color: var(--white);
}

.edit-btn {
    background: var(--warning-color);
    color: var(--white);
}

.status-btn {
    background: var(--success-color);
    color: var(--white);
}

.parts-btn {
    background: var(--secondary-color);
    color: var(--white);
}

.print-btn {
    background: var(--gray-600);
    color: var(--white);
}

.delete-btn {
    background: var(--danger-color);
    color: var(--white);
}

.action-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* النماذج */
.form-section {
    margin-bottom: 25px;
    padding: 20px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.form-section h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h4 i {
    color: var(--primary-color);
}

/* قطع الغيار */
.parts-list {
    margin-bottom: 20px;
}

.part-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.part-info {
    flex: 1;
}

.part-name {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 4px;
}

.part-details {
    font-size: 0.85rem;
    color: var(--gray-500);
}

.part-cost {
    font-weight: 700;
    color: var(--success-color);
    font-size: 1.1rem;
}

.add-part-btn {
    background: var(--success-color);
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.add-part-btn:hover {
    background: #059669;
}

/* نافذة اختيار قطع الغيار */
.parts-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.available-parts {
    background: var(--gray-50);
    padding: 15px;
    border-radius: var(--border-radius);
}

.selected-parts {
    background: var(--light-gray);
    padding: 15px;
    border-radius: var(--border-radius);
}

.parts-selector h4 {
    margin-bottom: 15px;
    font-weight: 600;
    color: var(--dark-color);
}

.part-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.part-option:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.part-option.selected {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

/* إيصال الاستلام */
.receipt-preview {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    font-family: 'Courier New', monospace;
    line-height: 1.6;
}

.receipt-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--dark-color);
    padding-bottom: 15px;
}

.receipt-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.receipt-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.receipt-section {
    margin-bottom: 15px;
}

.receipt-section h4 {
    font-weight: 700;
    margin-bottom: 8px;
    text-decoration: underline;
}

.receipt-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid var(--gray-300);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .maintenance-table {
        font-size: 0.8rem;
    }
    
    .maintenance-table th,
    .maintenance-table td {
        padding: 8px 6px;
    }
    
    .actions {
        flex-direction: column;
        gap: 4px;
    }
    
    .action-btn {
        font-size: 0.75rem;
        padding: 4px 8px;
    }
}

@media (max-width: 768px) {
    .table-wrapper {
        overflow-x: auto;
    }
    
    .maintenance-table {
        min-width: 1000px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .parts-selector {
        grid-template-columns: 1fr;
    }
    
    .receipt-info {
        grid-template-columns: 1fr;
    }
    
    .controls-section {
        flex-direction: column;
        gap: 15px;
    }
    
    .search-controls {
        flex-direction: column;
    }
    
    .filter-controls {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .action-controls {
        justify-content: center;
    }
}

/* تأثيرات خاصة */
.urgent-repair {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

.completed-repair {
    background: linear-gradient(45deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
}

/* تحسينات الطباعة */
@media print {
    .receipt-preview {
        box-shadow: none;
        border: 1px solid var(--dark-color);
    }
    
    .modal,
    .sidebar,
    .top-bar,
    .controls-section,
    .stats-grid,
    .table-header,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
    }
    
    .page-content {
        padding: 0;
    }
}

/* تحسينات إضافية */
.priority-high {
    border-left: 4px solid var(--danger-color);
}

.priority-medium {
    border-left: 4px solid var(--warning-color);
}

.priority-low {
    border-left: 4px solid var(--success-color);
}

.overdue {
    background: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.warranty-info {
    background: rgba(16, 185, 129, 0.1);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    color: var(--success-color);
    font-weight: 600;
}

.no-warranty {
    background: rgba(107, 114, 128, 0.1);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    color: var(--gray-500);
}
