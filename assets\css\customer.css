/* واجهة العملاء - Customer Interface */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap');

:root {
    /* الألوان الرئيسية */
    --primary-color: #0057D9;
    --secondary-color: #1C1C1E;
    --accent-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --white: #FFFFFF;
    --light-gray: #f8f9fa;
    --medium-gray: #6c757d;
    --dark-gray: #343a40;

    /* تدرجات الألوان */
    --primary-gradient: linear-gradient(135deg, #0057D9 0%, #0041a3 100%);
    --secondary-gradient: linear-gradient(135deg, #1C1C1E 0%, #000000 100%);
    --accent-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);

    /* الظلال */
    --shadow-light: 0 2px 10px rgba(0, 87, 217, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 87, 217, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 87, 217, 0.2);

    /* الانتقالات */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* الحدود */
    --border-radius: 12px;
    --border-radius-small: 8px;
    --border-radius-large: 20px;
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--secondary-color);
    background: var(--white);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header الثابت */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--white);
    box-shadow: var(--shadow-light);
    z-index: 1000;
    transition: var(--transition-normal);
}

.main-header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-color);
    text-decoration: none;
}

.logo i {
    font-size: 2rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
    align-items: center;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600;
    border-radius: var(--border-radius-small);
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(0, 87, 217, 0.1);
}

.nav-link.admin-link {
    background: var(--primary-gradient);
    color: var(--white);
}

.nav-link.admin-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.cart-btn,
.theme-toggle,
.mobile-menu-toggle {
    position: relative;
    background: none;
    border: none;
    padding: 12px;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--secondary-color);
}

.cart-btn:hover,
.theme-toggle:hover,
.mobile-menu-toggle:hover {
    background: var(--light-gray);
    color: var(--primary-color);
}

.cart-count {
    position: absolute;
    top: 5px;
    right: 5px;
    background: var(--danger-color);
    color: var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
}

.mobile-menu-toggle {
    display: none;
}

/* Hero Section */
.hero-section {
    margin-top: 80px;
    background: var(--primary-gradient);
    color: var(--white);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-content .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text h1 {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 20px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--white);
    color: var(--primary-color);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
}

.hero-image img {
    width: 100%;
    max-width: 400px;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
}

/* شريط المميزات */
.features-bar {
    background: var(--white);
    padding: 30px 0;
    box-shadow: var(--shadow-light);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    transition: var(--transition-normal);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.feature-item i {
    font-size: 2rem;
    color: var(--primary-color);
    background: rgba(0, 87, 217, 0.1);
    padding: 15px;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-item span {
    font-weight: 600;
    color: var(--secondary-color);
}

/* أقسام عامة */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--secondary-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.section-header h2 i {
    color: var(--primary-color);
}

.section-header p {
    font-size: 1.1rem;
    color: var(--medium-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* العروض المميزة */
.special-offers {
    background: var(--light-gray);
}

.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.offer-card {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    position: relative;
}

.offer-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.offer-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--danger-color);
    color: var(--white);
    padding: 8px 15px;
    border-radius: var(--border-radius-large);
    font-weight: 700;
    font-size: 0.9rem;
    z-index: 3;
}

.offer-card .product-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.offer-card .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.offer-card:hover .product-image img {
    transform: scale(1.1);
}

.offer-card .product-info {
    padding: 25px;
}

.offer-card .product-info h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.price-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.current-price {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-color);
}

.old-price {
    font-size: 1.1rem;
    color: var(--medium-gray);
    text-decoration: line-through;
}

.add-to-cart-btn {
    width: 100%;
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    padding: 12px;
    border-radius: var(--border-radius-small);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* المنتجات المميزة */
.product-categories {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.category-btn {
    padding: 12px 25px;
    border: 2px solid var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    border-radius: var(--border-radius-large);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.category-btn.active,
.category-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.product-card {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.product-card .product-image {
    height: 220px;
    overflow: hidden;
    position: relative;
}

.product-card .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 87, 217, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.quick-view-btn {
    background: var(--white);
    color: var(--primary-color);
    border: none;
    padding: 12px;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.quick-view-btn:hover {
    transform: scale(1.1);
}

.product-card .product-info {
    padding: 20px;
}

.product-card .product-info h3 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--secondary-color);
}

.product-category {
    font-size: 0.9rem;
    color: var(--medium-gray);
    margin-bottom: 10px;
}

.product-card .price {
    font-size: 1.3rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stock-info {
    margin-bottom: 15px;
}

.stock-count {
    font-size: 0.9rem;
    color: var(--accent-color);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-content .container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .offers-grid,
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .product-categories {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .category-btn {
        white-space: nowrap;
    }
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition-slow);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* الوضع الليلي */
body.dark-mode {
    --white: #1a1a1a;
    --light-gray: #2d2d2d;
    --secondary-color: #ffffff;
    --medium-gray: #b0b0b0;
}

body.dark-mode .main-header {
    background: var(--white);
    border-bottom: 1px solid #333;
}

body.dark-mode .offer-card,
body.dark-mode .product-card {
    background: var(--light-gray);
    border: 1px solid #333;
}

/* قسم الصيانة */
.maintenance-section {
    background: var(--light-gray);
}

.maintenance-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.service-types {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.service-item {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.service-item i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    background: rgba(0, 87, 217, 0.1);
    padding: 20px;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.service-item h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--secondary-color);
}

.service-item p {
    color: var(--medium-gray);
    line-height: 1.6;
}

.maintenance-form {
    background: var(--white);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
}

.maintenance-form h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--secondary-color);
    text-align: center;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--secondary-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius-small);
    font-size: 1rem;
    transition: var(--transition-fast);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 87, 217, 0.1);
}

/* آراء العملاء */
.testimonials {
    background: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background: var(--light-gray);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: var(--primary-color);
    font-weight: 700;
    opacity: 0.3;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.stars {
    display: flex;
    gap: 5px;
    margin-bottom: 20px;
}

.stars i {
    color: #ffc107;
    font-size: 1.2rem;
}

.testimonial-card p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 25px;
    color: var(--secondary-color);
    font-style: italic;
}

.customer-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.customer-info img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

.customer-info h4 {
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.customer-info span {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

/* قسم التواصل */
.contact-section {
    background: var(--light-gray);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
}

.contact-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.contact-item i {
    font-size: 2rem;
    color: var(--primary-color);
    background: rgba(0, 87, 217, 0.1);
    padding: 15px;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-item h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--secondary-color);
}

.contact-item p {
    color: var(--medium-gray);
    margin-bottom: 10px;
}

.map-link,
.call-link,
.whatsapp-link,
.email-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-fast);
}

.map-link:hover,
.call-link:hover,
.whatsapp-link:hover,
.email-link:hover {
    color: var(--accent-color);
}

.whatsapp-link {
    color: #25d366;
}

.whatsapp-link:hover {
    color: #128c7e;
}

.contact-form {
    background: var(--white);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
}

.contact-form h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--secondary-color);
    text-align: center;
}

/* الفوتر */
.main-footer {
    background: var(--secondary-gradient);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--white);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 20px;
}

.footer-logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.footer-section p {
    line-height: 1.8;
    margin-bottom: 20px;
    opacity: 0.9;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition-normal);
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--white);
    padding-right: 10px;
}

.footer-section .contact-info p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.footer-section .contact-info i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

/* الأزرار العائمة */
.floating-buttons {
    position: fixed;
    bottom: 30px;
    left: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 1000;
}

.whatsapp-float,
.call-float,
.scroll-top {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.5rem;
    box-shadow: var(--shadow-heavy);
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
}

.whatsapp-float {
    background: #25d366;
    color: var(--white);
}

.whatsapp-float:hover {
    background: #128c7e;
    transform: scale(1.1);
}

.call-float {
    background: var(--primary-color);
    color: var(--white);
}

.call-float:hover {
    background: #0041a3;
    transform: scale(1.1);
}

.scroll-top {
    background: var(--secondary-color);
    color: var(--white);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.scroll-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

/* سلة التسوق */
.cart-sidebar {
    position: fixed;
    top: 0;
    left: -400px;
    width: 400px;
    height: 100vh;
    background: var(--white);
    box-shadow: var(--shadow-heavy);
    z-index: 1001;
    transition: var(--transition-normal);
    display: flex;
    flex-direction: column;
}

.cart-sidebar.open {
    left: 0;
}

.cart-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--light-gray);
}

.cart-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.close-cart {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--medium-gray);
    transition: var(--transition-fast);
}

.close-cart:hover {
    color: var(--danger-color);
}

.cart-items {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.cart-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    background: var(--light-gray);
}

.cart-total {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    color: var(--secondary-color);
}

.checkout-btn {
    width: 100%;
    background: var(--accent-gradient);
    color: var(--white);
    border: none;
    padding: 15px;
    border-radius: var(--border-radius);
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.checkout-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* نافذة عرض المنتج */
.product-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1002;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.product-modal.open {
    opacity: 1;
    visibility: visible;
}

.product-modal .modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close-modal {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--danger-color);
    color: var(--white);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    z-index: 10;
    transition: var(--transition-fast);
}

.close-modal:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* تحسينات الأداء */
.product-image img,
.hero-image img {
    will-change: transform;
}

.btn,
.nav-link,
.product-card {
    will-change: transform;
}

/* Responsive Design للأقسام الجديدة */
@media (max-width: 768px) {
    .maintenance-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .service-types {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .floating-buttons {
        left: 20px;
        bottom: 20px;
    }

    .whatsapp-float,
    .call-float,
    .scroll-top {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .cart-sidebar {
        width: 100%;
        left: -100%;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }
}
