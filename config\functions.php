<?php
// وظائف مساعدة إضافية - محمية من التضارب

/**
 * إنشاء رقم فاتورة فريد
 */
if (!function_exists('generateInvoiceNumber')) {
    function generateInvoiceNumber() {
        $prefix = 'INV';
        $date = date('Ymd');
        $random = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        return $prefix . $date . $random;
    }
}

/**
 * إنشاء باركود فريد
 */
if (!function_exists('generateBarcode')) {
    function generateBarcode() {
        return date('Ymd') . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
    }
}

/**
 * تحويل النص إلى slug
 */
if (!function_exists('createSlug')) {
    function createSlug($text) {
        $text = trim($text);
        $text = preg_replace('/[^a-zA-Z0-9\s\u0600-\u06FF]/', '', $text);
        $text = preg_replace('/\s+/', '-', $text);
        return strtolower($text);
    }
}

/**
 * التحقق من صحة رقم الهاتف العراقي
 */
if (!function_exists('validateIraqiPhone')) {
    function validateIraqiPhone($phone) {
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        if (strlen($phone) == 11 && substr($phone, 0, 3) == '964') {
            return true; // رقم دولي
        } elseif (strlen($phone) == 11 && substr($phone, 0, 2) == '07') {
            return true; // رقم محلي
        }
        
        return false;
    }
}

/**
 * تنسيق رقم الهاتف
 */
if (!function_exists('formatPhone')) {
    function formatPhone($phone) {
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        if (strlen($phone) == 11 && substr($phone, 0, 2) == '07') {
            return substr($phone, 0, 4) . '-' . substr($phone, 4, 3) . '-' . substr($phone, 7);
        }
        
        return $phone;
    }
}

/**
 * حساب النسبة المئوية
 */
if (!function_exists('calculatePercentage')) {
    function calculatePercentage($part, $total) {
        if ($total == 0) return 0;
        return round(($part / $total) * 100, 2);
    }
}

/**
 * حساب الخصم
 */
if (!function_exists('calculateDiscount')) {
    function calculateDiscount($original, $discounted) {
        if ($original == 0) return 0;
        return round((($original - $discounted) / $original) * 100, 2);
    }
}

/**
 * إنشاء مجلد إذا لم يكن موجوداً
 */
if (!function_exists('createDirectoryIfNotExists')) {
    function createDirectoryIfNotExists($path) {
        if (!is_dir($path)) {
            return mkdir($path, 0755, true);
        }
        return true;
    }
}

/**
 * حذف ملف بأمان
 */
if (!function_exists('safeDeleteFile')) {
    function safeDeleteFile($filePath) {
        if (file_exists($filePath) && is_file($filePath)) {
            return unlink($filePath);
        }
        return false;
    }
}

/**
 * تسجيل الأنشطة
 */
if (!function_exists('logActivity')) {
    function logActivity($action, $details = '', $userId = null) {
        global $pdo;
        
        if (!$userId && isset($_SESSION['user_id'])) {
            $userId = $_SESSION['user_id'];
        }
        
        try {
            $sql = "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $userId,
                $action,
                $details,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];
            
            executeQuery($sql, $params);
        } catch (Exception $e) {
            error_log('Activity log error: ' . $e->getMessage());
        }
    }
}

/**
 * التحقق من انتهاء صلاحية الجلسة
 */
if (!function_exists('checkSessionExpiry')) {
    function checkSessionExpiry() {
        if (isset($_SESSION['last_activity'])) {
            $sessionTimeout = 3600; // ساعة واحدة
            if (time() - $_SESSION['last_activity'] > $sessionTimeout) {
                session_destroy();
                return false;
            }
        }
        $_SESSION['last_activity'] = time();
        return true;
    }
}

/**
 * تشفير البيانات الحساسة
 */
if (!function_exists('encryptData')) {
    function encryptData($data, $key = null) {
        if (!$key) {
            $key = 'irjnpfzw_fajir_secret_key';
        }
        
        $cipher = 'AES-256-CBC';
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($cipher));
        $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
}

/**
 * فك تشفير البيانات
 */
if (!function_exists('decryptData')) {
    function decryptData($encryptedData, $key = null) {
        if (!$key) {
            $key = 'irjnpfzw_fajir_secret_key';
        }
        
        $cipher = 'AES-256-CBC';
        $data = base64_decode($encryptedData);
        $ivLength = openssl_cipher_iv_length($cipher);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);
        
        return openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
    }
}

/**
 * إرسال إشعار واتساب (مؤقتاً معطل)
 */
if (!function_exists('sendWhatsAppNotification')) {
    function sendWhatsAppNotification($phone, $message) {
        // مؤقتاً معطل - يحتاج API خارجي
        return false;
    }
}
?>
