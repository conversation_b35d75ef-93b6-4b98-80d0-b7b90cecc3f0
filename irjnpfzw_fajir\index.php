<?php
session_start();
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المحل</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-store"></i> نظام إدارة المحل</h1>
                <div class="user-info">
                    <span>مرحباً، <?php echo htmlspecialchars($user_name); ?></span>
                    <span class="role">(<?php echo $user_role == 'admin' ? 'مدير' : 'موظف'; ?>)</span>
                    <a href="auth/logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <!-- Navigation Menu -->
        <nav class="main-nav">
            <div class="nav-grid">
                <!-- نقطة البيع -->
                <a href="pos/index.php" class="nav-item pos">
                    <i class="fas fa-cash-register"></i>
                    <span>نقطة البيع</span>
                </a>

                <!-- إدارة المنتجات -->
                <a href="products/index.php" class="nav-item products">
                    <i class="fas fa-boxes"></i>
                    <span>إدارة المنتجات</span>
                </a>

                <!-- إدارة العملاء -->
                <a href="customers/index.php" class="nav-item customers">
                    <i class="fas fa-users"></i>
                    <span>إدارة العملاء</span>
                </a>

                <!-- إدارة الموردين -->
                <a href="suppliers/index.php" class="nav-item suppliers">
                    <i class="fas fa-truck"></i>
                    <span>إدارة الموردين</span>
                </a>

                <!-- نظام الصيانة -->
                <a href="maintenance/index.php" class="nav-item maintenance">
                    <i class="fas fa-tools"></i>
                    <span>نظام الصيانة</span>
                </a>

                <!-- التقارير -->
                <a href="reports/index.php" class="nav-item reports">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير والإحصائيات</span>
                </a>

                <?php if ($user_role == 'admin'): ?>
                <!-- الإعدادات (للمدير فقط) -->
                <a href="settings/index.php" class="nav-item settings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
                <?php endif; ?>
            </div>
        </nav>

        <!-- Dashboard Stats -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <i class="fas fa-dollar-sign"></i>
                <div class="stat-info">
                    <h3>مبيعات اليوم</h3>
                    <p id="today-sales">0 د.ع</p>
                </div>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-shopping-cart"></i>
                <div class="stat-info">
                    <h3>عدد الفواتير</h3>
                    <p id="today-invoices">0</p>
                </div>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-tools"></i>
                <div class="stat-info">
                    <h3>أجهزة قيد الصيانة</h3>
                    <p id="maintenance-count">0</p>
                </div>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-exclamation-triangle"></i>
                <div class="stat-info">
                    <h3>منتجات منخفضة المخزون</h3>
                    <p id="low-stock">0</p>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        // تحديث الإحصائيات
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardStats();
        });

        function loadDashboardStats() {
            fetch('api/dashboard_stats.php')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('today-sales').textContent = data.today_sales + ' د.ع';
                    document.getElementById('today-invoices').textContent = data.today_invoices;
                    document.getElementById('maintenance-count').textContent = data.maintenance_count;
                    document.getElementById('low-stock').textContent = data.low_stock;
                })
                .catch(error => console.error('Error:', error));
        }
    </script>
</body>
</html>
