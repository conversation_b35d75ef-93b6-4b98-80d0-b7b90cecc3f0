<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

$user_role = $_SESSION['user_role'];
$user_name = $_SESSION['user_name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام إدارة المحل</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/customers.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    <span>إدارة المحل</span>
                </div>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="../admin/dashboard.php" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li>
                        <a href="../pos/index.php" class="nav-link">
                            <i class="fas fa-cash-register"></i>
                            <span>نقطة البيع</span>
                        </a>
                    </li>
                    <li>
                        <a href="../products/index.php" class="nav-link">
                            <i class="fas fa-boxes"></i>
                            <span>إدارة المنتجات</span>
                        </a>
                    </li>
                    <li>
                        <a href="index.php" class="nav-link active">
                            <i class="fas fa-users"></i>
                            <span>إدارة العملاء</span>
                        </a>
                    </li>
                    <li>
                        <a href="../suppliers/index.php" class="nav-link">
                            <i class="fas fa-truck"></i>
                            <span>إدارة الموردين</span>
                        </a>
                    </li>
                    <li>
                        <a href="../maintenance/index.php" class="nav-link">
                            <i class="fas fa-tools"></i>
                            <span>نظام الصيانة</span>
                        </a>
                    </li>
                    <li>
                        <a href="../reports/index.php" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <?php if ($user_role === 'admin'): ?>
                    <li>
                        <a href="../settings/index.php" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name"><?php echo htmlspecialchars($user_name); ?></span>
                        <span class="user-role"><?php echo $user_role === 'admin' ? 'مدير' : 'موظف'; ?></span>
                    </div>
                </div>
                <a href="../auth/logout.php" class="logout-btn" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>إدارة العملاء</h1>
                    <div class="breadcrumb">
                        <span>الرئيسية</span>
                        <i class="fas fa-chevron-left"></i>
                        <span>إدارة العملاء</span>
                    </div>
                </div>
                
                <div class="top-bar-right">
                    <div class="datetime">
                        <div id="current-time"></div>
                        <div id="current-date"></div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                <!-- Controls Section -->
                <div class="controls-section">
                    <div class="search-controls">
                        <div class="search-box">
                            <input type="text" id="search-input" placeholder="البحث بالاسم أو رقم الهاتف...">
                            <button id="search-btn" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        
                        <div class="filter-controls">
                            <select id="debt-filter">
                                <option value="">جميع العملاء</option>
                                <option value="with_debt">عملاء لديهم ديون</option>
                                <option value="no_debt">عملاء بدون ديون</option>
                            </select>
                        </div>
                    </div>

                    <div class="action-controls">
                        <button id="add-customer" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة عميل
                        </button>
                        <button id="export-excel" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button id="import-excel" class="btn btn-secondary">
                            <i class="fas fa-upload"></i> استيراد Excel
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>إجمالي العملاء</h3>
                            <p class="stat-value" id="total-customers">0</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-info">
                            <h3>عملاء جدد هذا الشهر</h3>
                            <p class="stat-value" id="new-customers">0</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>عملاء لديهم ديون</h3>
                            <p class="stat-value" id="customers-with-debt">0</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3>إجمالي الديون</h3>
                            <p class="stat-value" id="total-debt">0 د.ع</p>
                        </div>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="table-container">
                    <div class="table-header">
                        <h3><i class="fas fa-users"></i> قائمة العملاء</h3>
                        <div class="table-actions">
                            <button class="btn-small" onclick="CustomersManager.loadCustomers()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="customers-table" id="customers-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>العنوان</th>
                                    <th>إجمالي الديون</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="customers-tbody">
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination" id="pagination">
                    <!-- أزرار التصفح -->
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة إضافة/تعديل عميل -->
    <div id="customer-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">إضافة عميل جديد</h3>
                <button class="close-modal">&times;</button>
            </div>
            <form id="customer-form">
                <input type="hidden" id="customer-id" name="id">
                
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم العميل *</label>
                        <input type="text" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" name="phone">
                    </div>
                </div>

                <div class="form-group">
                    <label>العنوان</label>
                    <textarea name="address" rows="3"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>البريد الإلكتروني</label>
                        <input type="email" name="email">
                    </div>
                    <div class="form-group">
                        <label>إجمالي الديون</label>
                        <input type="number" name="total_debt" min="0" step="0.01" value="0" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label>ملاحظات</label>
                    <textarea name="notes" rows="3"></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="btn btn-secondary close-modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة كشف حساب العميل -->
    <div id="statement-modal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="statement-title">كشف حساب العميل</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="statement-content">
                    <!-- سيتم تحميل كشف الحساب هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة دفعة -->
    <div id="payment-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة دفعة</h3>
                <button class="close-modal">&times;</button>
            </div>
            <form id="payment-form">
                <input type="hidden" id="payment-customer-id" name="customer_id">
                
                <div class="form-group">
                    <label>المبلغ المدفوع *</label>
                    <input type="number" name="amount" min="0" step="0.01" required>
                </div>

                <div class="form-group">
                    <label>طريقة الدفع</label>
                    <select name="payment_method">
                        <option value="cash">نقدي</option>
                        <option value="bank_transfer">تحويل بنكي</option>
                        <option value="check">شيك</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>ملاحظات</label>
                    <textarea name="notes" rows="3"></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الدفعة
                    </button>
                    <button type="button" class="btn btn-secondary close-modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script src="../assets/js/customers.js"></script>
    <script>
        // تهيئة صفحة العملاء
        document.addEventListener('DOMContentLoaded', function() {
            CustomersManager.init();
        });
    </script>
</body>
</html>
