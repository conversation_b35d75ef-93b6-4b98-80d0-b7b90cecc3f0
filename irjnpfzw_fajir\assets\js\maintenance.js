// نظام الصيانة
const MaintenanceManager = {
    currentPage: 1,
    totalPages: 1,
    maintenance: [],
    filters: {
        search: '',
        status: '',
        date: ''
    },
    selectedMaintenance: null,

    // تهيئة النظام
    init() {
        this.bindEvents();
        this.loadMaintenance();
        this.loadStats();
        
        // تهيئة الشريط الجانبي
        AdminDashboard.init();
    },

    // ربط الأحداث
    bindEvents() {
        // البحث والفلاتر
        document.getElementById('search-input').addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.currentPage = 1;
            this.loadMaintenance();
        });

        document.getElementById('status-filter').addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.currentPage = 1;
            this.loadMaintenance();
        });

        document.getElementById('date-filter').addEventListener('change', (e) => {
            this.filters.date = e.target.value;
            this.currentPage = 1;
            this.loadMaintenance();
        });

        // أزرار العمليات
        document.getElementById('receive-device').addEventListener('click', () => {
            this.showDeviceModal();
        });

        document.getElementById('print-receipt').addEventListener('click', () => {
            this.printReceipt();
        });

        document.getElementById('export-report').addEventListener('click', () => {
            this.exportReport();
        });

        // النماذج
        document.getElementById('device-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDevice();
        });

        document.getElementById('status-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.updateStatus();
        });

        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                this.hideModals();
            });
        });

        // إغلاق النوافذ عند النقر خارجها
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideModals();
                }
            });
        });
    },

    // تحميل بيانات الصيانة
    async loadMaintenance() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: 20,
                search: this.filters.search,
                status: this.filters.status,
                date: this.filters.date
            });

            const response = await fetch(`../api/maintenance.php?${params}`);
            const data = await response.json();

            if (response.ok) {
                this.maintenance = data.maintenance;
                this.totalPages = data.pages;
                this.displayMaintenance();
                this.updatePagination();
            } else {
                throw new Error(data.error || 'خطأ في تحميل بيانات الصيانة');
            }
        } catch (error) {
            console.error('Error loading maintenance:', error);
            showMessage('خطأ في تحميل بيانات الصيانة: ' + error.message, 'error');
            this.showEmptyState();
        }
    },

    // تحميل الإحصائيات
    async loadStats() {
        try {
            const response = await fetch('../api/maintenance_stats.php');
            const stats = await response.json();

            if (response.ok) {
                document.getElementById('received-count').textContent = stats.received;
                document.getElementById('progress-count').textContent = stats.in_progress;
                document.getElementById('ready-count').textContent = stats.ready;
                document.getElementById('delivered-count').textContent = stats.delivered;
            }
        } catch (error) {
            console.error('Error loading stats:', error);
        }
    },

    // عرض بيانات الصيانة
    displayMaintenance() {
        const tbody = document.getElementById('maintenance-tbody');
        
        if (this.maintenance.length === 0) {
            this.showEmptyState();
            return;
        }

        tbody.innerHTML = this.maintenance.map(item => `
            <tr class="${this.getRowClass(item)}">
                <td>
                    <span class="order-number">#${item.id}</span>
                </td>
                <td class="customer-name">${item.customer_name}</td>
                <td>
                    ${item.customer_phone ? `<span class="customer-phone">${item.customer_phone}</span>` : '-'}
                </td>
                <td class="device-info">
                    <div class="device-type">${item.device_type}</div>
                    ${item.device_model ? `<div class="device-model">${item.device_model}</div>` : ''}
                </td>
                <td class="problem-description" title="${item.problem_description}">
                    ${item.problem_description}
                </td>
                <td>
                    <span class="cost-amount estimated-cost">${formatMoney(item.estimated_cost || 0)}</span>
                </td>
                <td>
                    <span class="cost-amount actual-cost">${formatMoney(item.actual_cost || 0)}</span>
                </td>
                <td>
                    <span class="status-badge status-${item.status}">
                        ${this.getStatusText(item.status)}
                    </span>
                </td>
                <td class="date-info">
                    <div class="date-received">${formatDate(item.received_date)}</div>
                    ${item.delivery_date ? `<div class="date-delivered">${formatDate(item.delivery_date)}</div>` : ''}
                </td>
                <td class="actions">
                    <button class="action-btn view-btn" onclick="MaintenanceManager.viewDetails(${item.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit-btn" onclick="MaintenanceManager.editDevice(${item.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn status-btn" onclick="MaintenanceManager.showStatusModal(${item.id})" title="تحديث الحالة">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button class="action-btn parts-btn" onclick="MaintenanceManager.manageParts(${item.id})" title="قطع الغيار">
                        <i class="fas fa-cogs"></i>
                    </button>
                    <button class="action-btn print-btn" onclick="MaintenanceManager.printReceipt(${item.id})" title="طباعة إيصال">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="action-btn delete-btn" onclick="MaintenanceManager.deleteDevice(${item.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    },

    // الحصول على فئة الصف
    getRowClass(item) {
        let classes = [];
        
        // إضافة فئة حسب الحالة
        if (item.status === 'ready') {
            classes.push('completed-repair');
        }
        
        // إضافة فئة للإصلاحات المتأخرة (أكثر من 7 أيام)
        const receivedDate = new Date(item.received_date);
        const daysDiff = (new Date() - receivedDate) / (1000 * 60 * 60 * 24);
        if (daysDiff > 7 && item.status !== 'delivered') {
            classes.push('overdue');
        }
        
        return classes.join(' ');
    },

    // الحصول على نص الحالة
    getStatusText(status) {
        const statuses = {
            'received': 'مستلم',
            'in_progress': 'قيد التصليح',
            'ready': 'جاهز للتسليم',
            'delivered': 'مسلم'
        };
        return statuses[status] || status;
    },

    // عرض حالة فارغة
    showEmptyState() {
        const tbody = document.getElementById('maintenance-tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="empty-state">
                    <i class="fas fa-tools"></i>
                    <h3>لا توجد أجهزة</h3>
                    <p>لم يتم العثور على أجهزة تطابق البحث</p>
                </td>
            </tr>
        `;
    },

    // عرض حالة التحميل
    showLoading() {
        const tbody = document.getElementById('maintenance-tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>جاري التحميل...</p>
                </td>
            </tr>
        `;
    },

    // تحديث التصفح
    updatePagination() {
        const pagination = document.getElementById('pagination');
        
        if (this.totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let html = '';
        
        // زر السابق
        html += `
            <button ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="MaintenanceManager.goToPage(${this.currentPage - 1})">
                <i class="fas fa-chevron-right"></i> السابق
            </button>
        `;

        // أرقام الصفحات
        for (let i = 1; i <= this.totalPages; i++) {
            if (i === this.currentPage || 
                i === 1 || 
                i === this.totalPages || 
                (i >= this.currentPage - 1 && i <= this.currentPage + 1)) {
                html += `
                    <button class="${i === this.currentPage ? 'active' : ''}" 
                            onclick="MaintenanceManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.currentPage - 2 || i === this.currentPage + 2) {
                html += '<span>...</span>';
            }
        }

        // زر التالي
        html += `
            <button ${this.currentPage === this.totalPages ? 'disabled' : ''} 
                    onclick="MaintenanceManager.goToPage(${this.currentPage + 1})">
                التالي <i class="fas fa-chevron-left"></i>
            </button>
        `;

        pagination.innerHTML = html;
    },

    // الانتقال لصفحة
    goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            this.loadMaintenance();
        }
    },

    // عرض نافذة استقبال جهاز
    showDeviceModal(device = null) {
        const modal = document.getElementById('device-modal');
        const form = document.getElementById('device-form');
        const title = document.getElementById('modal-title');
        
        if (device) {
            title.textContent = 'تعديل بيانات الجهاز';
            this.fillDeviceForm(device);
        } else {
            title.textContent = 'استقبال جهاز للصيانة';
            form.reset();
            document.getElementById('maintenance-id').value = '';
        }
        
        modal.classList.add('show');
    },

    // ملء نموذج الجهاز
    fillDeviceForm(device) {
        document.getElementById('maintenance-id').value = device.id;
        document.querySelector('[name="customer_name"]').value = device.customer_name;
        document.querySelector('[name="customer_phone"]').value = device.customer_phone || '';
        document.querySelector('[name="device_type"]').value = device.device_type;
        document.querySelector('[name="device_model"]').value = device.device_model || '';
        document.querySelector('[name="problem_description"]').value = device.problem_description;
        document.querySelector('[name="estimated_cost"]').value = device.estimated_cost || 0;
        document.querySelector('[name="actual_cost"]').value = device.actual_cost || 0;
        document.querySelector('[name="notes"]').value = device.notes || '';
        document.querySelector('[name="technician_notes"]').value = device.technician_notes || '';
    },

    // حفظ الجهاز
    async saveDevice() {
        try {
            const form = document.getElementById('device-form');
            const formData = new FormData(form);
            const deviceId = document.getElementById('maintenance-id').value;
            
            // تحويل FormData إلى Object
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            const url = '../api/maintenance.php';
            const method = deviceId ? 'PUT' : 'POST';
            
            if (deviceId) {
                data.id = deviceId;
            }
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage(result.message, 'success');
                this.hideModals();
                this.loadMaintenance();
                this.loadStats();
                
                // إذا كان جهاز جديد، اعرض خيار طباعة الإيصال
                if (!deviceId && result.maintenance_id) {
                    const printReceipt = await confirmAction('تم حفظ الجهاز بنجاح. هل تريد طباعة إيصال الاستلام؟');
                    if (printReceipt) {
                        this.printReceipt(result.maintenance_id);
                    }
                }
            } else {
                throw new Error(result.error || 'خطأ في حفظ الجهاز');
            }
        } catch (error) {
            console.error('Error saving device:', error);
            showMessage('خطأ في حفظ الجهاز: ' + error.message, 'error');
        }
    },

    // تعديل جهاز
    editDevice(id) {
        const device = this.maintenance.find(m => m.id === id);
        if (device) {
            this.showDeviceModal(device);
        }
    },

    // عرض نافذة تحديث الحالة
    showStatusModal(id) {
        const device = this.maintenance.find(m => m.id === id);
        if (!device) return;
        
        document.getElementById('status-maintenance-id').value = id;
        document.querySelector('#status-form [name="status"]').value = device.status;
        document.querySelector('#status-form [name="actual_cost"]').value = device.actual_cost || '';
        document.querySelector('#status-form [name="technician_notes"]').value = device.technician_notes || '';
        
        const modal = document.getElementById('status-modal');
        modal.classList.add('show');
    },

    // تحديث الحالة
    async updateStatus() {
        try {
            const form = document.getElementById('status-form');
            const formData = new FormData(form);
            
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            const response = await fetch('../api/maintenance.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage('تم تحديث الحالة بنجاح', 'success');
                this.hideModals();
                this.loadMaintenance();
                this.loadStats();
            } else {
                throw new Error(result.error || 'خطأ في تحديث الحالة');
            }
        } catch (error) {
            console.error('Error updating status:', error);
            showMessage('خطأ في تحديث الحالة: ' + error.message, 'error');
        }
    },

    // إدارة قطع الغيار
    async manageParts(id) {
        try {
            const response = await fetch(`../api/maintenance_parts.php?maintenance_id=${id}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'خطأ في جلب قطع الغيار');
            }

            const modal = document.getElementById('parts-modal');
            const content = document.getElementById('parts-content');
            
            content.innerHTML = `
                <div class="parts-list">
                    <h4>قطع الغيار المستخدمة</h4>
                    ${data.parts.length > 0 ? data.parts.map(part => `
                        <div class="part-item">
                            <div class="part-info">
                                <div class="part-name">${part.product_name}</div>
                                <div class="part-details">الكمية: ${part.quantity} - السعر: ${formatMoney(part.price)}</div>
                            </div>
                            <div class="part-cost">${formatMoney(part.quantity * part.price)}</div>
                        </div>
                    `).join('') : '<p>لم يتم استخدام قطع غيار</p>'}
                </div>
                
                <button class="add-part-btn" onclick="MaintenanceManager.addPart(${id})">
                    <i class="fas fa-plus"></i> إضافة قطعة غيار
                </button>
            `;
            
            modal.classList.add('show');
            
        } catch (error) {
            console.error('Error loading parts:', error);
            showMessage('خطأ في جلب قطع الغيار: ' + error.message, 'error');
        }
    },

    // طباعة إيصال
    async printReceipt(id) {
        try {
            const device = this.maintenance.find(m => m.id === id) || 
                          await this.fetchDeviceDetails(id);
            
            if (!device) {
                throw new Error('لم يتم العثور على الجهاز');
            }

            const receiptWindow = window.open('', '_blank');
            receiptWindow.document.write(this.generateReceiptHTML(device));
            receiptWindow.document.close();
            receiptWindow.print();
            
        } catch (error) {
            console.error('Error printing receipt:', error);
            showMessage('خطأ في طباعة الإيصال: ' + error.message, 'error');
        }
    },

    // إنشاء HTML للإيصال
    generateReceiptHTML(device) {
        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>إيصال استلام جهاز</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .receipt { max-width: 400px; margin: 0 auto; }
                    .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }
                    .info { margin: 15px 0; }
                    .footer { text-align: center; margin-top: 20px; border-top: 1px solid #000; padding-top: 10px; }
                </style>
            </head>
            <body>
                <div class="receipt">
                    <div class="header">
                        <h2>إيصال استلام جهاز للصيانة</h2>
                        <p>رقم الطلب: #${device.id}</p>
                    </div>
                    
                    <div class="info">
                        <p><strong>اسم العميل:</strong> ${device.customer_name}</p>
                        <p><strong>رقم الهاتف:</strong> ${device.customer_phone || '-'}</p>
                        <p><strong>نوع الجهاز:</strong> ${device.device_type}</p>
                        <p><strong>الموديل:</strong> ${device.device_model || '-'}</p>
                        <p><strong>المشكلة:</strong> ${device.problem_description}</p>
                        <p><strong>التكلفة المقدرة:</strong> ${formatMoney(device.estimated_cost || 0)}</p>
                        <p><strong>تاريخ الاستلام:</strong> ${formatDate(device.received_date || new Date())}</p>
                    </div>
                    
                    <div class="footer">
                        <p>شكراً لثقتكم بنا</p>
                        <p>للاستفسار: ${window.location.origin}</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    },

    // حذف جهاز
    async deleteDevice(id) {
        const confirmed = await confirmDelete('هل أنت متأكد من حذف هذا الجهاز؟');
        if (!confirmed) return;
        
        try {
            const response = await fetch('../api/maintenance.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id: id })
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                showMessage(result.message, 'success');
                this.loadMaintenance();
                this.loadStats();
            } else {
                throw new Error(result.error || 'خطأ في حذف الجهاز');
            }
        } catch (error) {
            console.error('Error deleting device:', error);
            showMessage('خطأ في حذف الجهاز: ' + error.message, 'error');
        }
    },

    // تصدير تقرير
    async exportReport() {
        try {
            const response = await fetch('../api/export_maintenance.php');
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `maintenance_report_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showMessage('تم تصدير التقرير بنجاح', 'success');
            } else {
                throw new Error('خطأ في تصدير التقرير');
            }
        } catch (error) {
            console.error('Error exporting report:', error);
            showMessage('خطأ في تصدير التقرير: ' + error.message, 'error');
        }
    },

    // إخفاء النوافذ المنبثقة
    hideModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
        });
    },

    // جلب تفاصيل الجهاز
    async fetchDeviceDetails(id) {
        try {
            const response = await fetch(`../api/maintenance.php?id=${id}`);
            const data = await response.json();
            return response.ok ? data : null;
        } catch (error) {
            console.error('Error fetching device details:', error);
            return null;
        }
    }
};
