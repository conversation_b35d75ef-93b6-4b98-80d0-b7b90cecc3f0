/* صفحة الإعدادات */

.settings-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow-x: auto;
}

.settings-tab {
    padding: 12px 20px;
    border: 2px solid var(--gray-200);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    font-weight: 500;
    font-size: 0.9rem;
}

.settings-tab:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.settings-tab.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.settings-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
}

.settings-card .card-header {
    padding: 25px 30px;
    border-bottom: 1px solid var(--gray-100);
}

.settings-card .card-header h3 {
    margin: 0 0 8px 0;
    color: var(--dark-color);
    font-size: 1.4rem;
    font-weight: 600;
}

.settings-card .card-header p {
    margin: 0;
    color: var(--gray-600);
    font-size: 0.95rem;
}

.settings-card .card-content {
    padding: 30px;
}

.form-section {
    margin-bottom: 35px;
    padding-bottom: 25px;
    border-bottom: 1px solid var(--gray-100);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h4 {
    margin: 0 0 20px 0;
    color: var(--dark-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--dark-color);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 87, 217, 0.1);
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: var(--gray-500);
    font-size: 0.85rem;
}

/* Color Picker Grid */
.color-picker-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.color-picker-item {
    text-align: center;
}

.color-picker-item label {
    display: block;
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: var(--gray-700);
}

.color-picker-item input[type="color"] {
    width: 60px;
    height: 60px;
    border: 3px solid var(--gray-200);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.color-picker-item input[type="color"]:hover {
    border-color: var(--primary-color);
    transform: scale(1.1);
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 15px;
}

.toggle-switch input[type="checkbox"] {
    width: 50px;
    height: 25px;
    appearance: none;
    background: var(--gray-300);
    border-radius: 25px;
    position: relative;
    cursor: pointer;
    transition: var(--transition);
}

.toggle-switch input[type="checkbox"]:checked {
    background: var(--primary-color);
}

.toggle-switch input[type="checkbox"]::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 21px;
    height: 21px;
    background: var(--white);
    border-radius: 50%;
    transition: var(--transition);
}

.toggle-switch input[type="checkbox"]:checked::before {
    transform: translateX(25px);
}

/* Categories Manager */
.categories-manager {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 20px;
}

.category-item,
.add-category {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.category-item:last-child,
.add-category {
    margin-bottom: 0;
}

.category-item input,
.add-category input {
    flex: 1;
    margin-bottom: 0;
}

/* Import Section */
.import-section {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.import-section input[type="file"] {
    flex: 1;
    min-width: 200px;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    padding-top: 25px;
    border-top: 1px solid var(--gray-100);
    margin-top: 30px;
}

/* Coming Soon Sections */
.coming-soon {
    text-align: center;
    padding: 60px 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.coming-soon h3 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: var(--gray-600);
}

.coming-soon p {
    color: var(--gray-500);
    font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .settings-nav {
        padding: 15px;
        gap: 8px;
    }
    
    .settings-tab {
        padding: 10px 15px;
        font-size: 0.85rem;
    }
    
    .settings-card .card-header,
    .settings-card .card-content {
        padding: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .color-picker-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }
    
    .import-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
