<?php
// ملف اختبار بسيط
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>اختبار النظام</h1>";

try {
    echo "<h2>1. اختبار تضمين الملفات</h2>";
    require_once 'config/database.php';
    echo "✅ تم تضمين database.php بنجاح<br>";
    
    echo "<h2>2. اختبار الوظائف</h2>";
    if (function_exists('formatMoney')) {
        echo "✅ وظيفة formatMoney موجودة<br>";
        echo "مثال: " . formatMoney(150000) . "<br>";
    } else {
        echo "❌ وظيفة formatMoney غير موجودة<br>";
    }
    
    if (function_exists('sanitize')) {
        echo "✅ وظيفة sanitize موجودة<br>";
    } else {
        echo "❌ وظيفة sanitize غير موجودة<br>";
    }
    
    echo "<h2>3. اختبار قاعدة البيانات</h2>";
    if (isset($pdo)) {
        echo "✅ متغير PDO موجود<br>";
        
        // اختبار الاتصال
        $result = $pdo->query("SELECT 1");
        if ($result) {
            echo "✅ الاتصال بقاعدة البيانات يعمل<br>";
        } else {
            echo "❌ مشكلة في الاتصال بقاعدة البيانات<br>";
        }
    } else {
        echo "❌ متغير PDO غير موجود<br>";
    }
    
    echo "<h2>4. اختبار الجداول</h2>";
    $tables = ['users', 'products', 'customers'];
    foreach ($tables as $table) {
        try {
            $result = $pdo->query("SELECT COUNT(*) FROM $table");
            if ($result) {
                $count = $result->fetchColumn();
                echo "✅ جدول $table موجود ويحتوي على $count سجل<br>";
            }
        } catch (Exception $e) {
            echo "❌ جدول $table غير موجود أو به مشكلة<br>";
        }
    }
    
    echo "<hr>";
    echo "<h2>النتيجة</h2>";
    echo "<p style='color: green; font-size: 18px;'>✅ النظام يعمل بشكل صحيح!</p>";
    echo "<p><a href='index.php'>انتقل للصفحة الرئيسية</a></p>";
    echo "<p><a href='check_system.php'>فحص شامل للنظام</a></p>";
    echo "<p><a href='update_database.php'>إنشاء قاعدة البيانات</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>خطأ:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>الحل:</strong> قم بتشغيل <a href='update_database.php'>update_database.php</a> أولاً</p>";
}
?>
