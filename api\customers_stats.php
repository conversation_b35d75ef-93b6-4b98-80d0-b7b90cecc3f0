<?php
header('Content-Type: application/json; charset=utf-8');
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

try {
    // إجمالي العملاء
    $total_customers = fetchOne("
        SELECT COUNT(*) as count 
        FROM customers 
        WHERE is_active = 1
    ")['count'];
    
    // العملاء الجدد هذا الشهر
    $new_customers = fetchOne("
        SELECT COUNT(*) as count 
        FROM customers 
        WHERE is_active = 1 
        AND MONTH(created_at) = MONTH(CURRENT_DATE()) 
        AND YEAR(created_at) = YEAR(CURRENT_DATE())
    ")['count'];
    
    // العملاء الذين لديهم ديون
    $customers_with_debt = fetchOne("
        SELECT COUNT(*) as count 
        FROM customers 
        WHERE is_active = 1 
        AND total_debt > 0
    ")['count'];
    
    // إجمالي الديون
    $total_debt = fetchOne("
        SELECT COALESCE(SUM(total_debt), 0) as total 
        FROM customers 
        WHERE is_active = 1
    ")['total'];
    
    echo json_encode([
        'total_customers' => $total_customers,
        'new_customers' => $new_customers,
        'customers_with_debt' => $customers_with_debt,
        'total_debt' => $total_debt
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في جلب الإحصائيات: ' . $e->getMessage()]);
}
?>
